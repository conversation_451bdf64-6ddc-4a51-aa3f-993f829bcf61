<template>
  <view class="notice-container">
    <!-- 修改自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">官方公告</text>
        <view class="right-area"></view>
      </view>
    </view>
    <!-- 公告列表 -->
    <scroll-view 
      class="notice-list"
      scroll-y
      @scrolltolower="loadMore"
      :style="{ height: scrollHeight + 'px' }"
    >
      <view 
        class="notice-item" 
        v-for="(item, index) in noticeList" 
        :key="index"
        @click="goToDetail(item)"
      >
        <view class="notice-content">
          <view class="notice-title">
            <text class="tag" :class="{ important: item.noticeType === 1 || item.isTop === 1 }">
              {{ getNoticeTypeText(item.noticeType) }}
            </text>
            {{ item.title }}
          </view>
          <rich-text class="notice-desc" :nodes="parseContent(item.content)"></rich-text>
          <view class="notice-footer">
            <text class="notice-time">{{ formatTime(item.createTime) }}</text>
          </view>
        </view>
        <view class="notice-right">
          <uni-icons type="right" size="16" color="#fff"></uni-icons>
        </view>
      </view>
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <text class="load-text">加载中...</text>
      </view>
      <view class="load-more" v-else>
        <text class="load-text">没有更多数据了</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      statusBarHeight: 0,
      scrollHeight: 0,
      page: 1,
      pageSize: 10,
      hasMore: true,
      total: 0,
      noticeList: []
    }
  },
  created() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    // 计算scroll-view高度需要减去导航栏高度
    this.scrollHeight = systemInfo.windowHeight
    this.loadNoticeList()
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    async loadNoticeList() {
      try {
        const res = await request({
          url: '/api/notice/list',
          method: 'GET',
          data: {
            page: this.page,
            size: this.pageSize
          }
        })
        if (res.code === 200 && res.data) {
          const { records, total, current, size } = res.data
          this.total = total
          if (this.page === 1) {
            this.noticeList = records
          } else {
            this.noticeList = [...this.noticeList, ...records]
          }
          this.hasMore = this.noticeList.length < total
          if (!records || records.length === 0) {
            this.hasMore = false
          }
        }
      } catch (e) {
        console.error('获取公告列表失败:', e)
        this.hasMore = false
      }
    },
    loadMore() {
      if (!this.hasMore) return
      this.page++
      this.loadNoticeList()
    },
    goToDetail(item) {
      uni.navigateTo({
        url: `/pages/noticedetail/index?id=${item.id}`
      })
    },
    formatTime(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    getNoticeTypeText(type) {
      switch(type) {
        case 1: return '重要';
        case 2: return '通知';
        case 3: return '系统';
        case 4: return '活动';
        case 5: return '维护';
        default: return '通知';
      }
    },
    parseContent(content) {
      if (!content) return '';
      content = content.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
      return content.replace(/<[^>]+>/g, match => {
        return match.replace(/style="[^"]*"/g, 'style="color: #FFD70099;"');
      });
    }
  }
}
</script>

<style lang="scss">
.notice-container {
  min-height: 100vh;
  background: #121212 !important;
  .custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #121212 !important;
    box-shadow: none;
    // border-bottom: 1px solid #222;
    .navbar-content {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;
      .left-area {
        width: 80rpx;
        height: 44px;
        display: flex;
        align-items: center;
        &:active {
          opacity: 0.7;
        }
      }
      .page-title {
        color: #fff;
        font-size: 30rpx;
        font-weight: 500;
      }
      .right-area {
        width: 80rpx;
      }
    }
  }
}
.notice-list {
  box-sizing: border-box;
  padding: calc(var(--status-bar-height) + 44px + 20rpx) 20rpx 20rpx;
}
.notice-item {
  margin-bottom: 20rpx;
  background: #18191D !important;
  backdrop-filter: blur(10px);
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: flex-start;
  // border: 1px solid #FFD70022;
  &:active {
    opacity: 0.8;
  }
  .notice-content {
    flex: 1;
    margin-right: 20rpx;
    .notice-title {
      color: #fff;
      font-size: 28rpx;
      font-weight: 500;
      margin-bottom: 16rpx;
      display: flex;
      align-items: center;
      .tag {
        font-size: 22rpx;
        padding: 4rpx 12rpx;
        border-radius: 6rpx;
        background: #FFD70022;
        color: #fff;
        margin-right: 12rpx;
        &.important {
          background: #F34A69 !important;
          color: #fff !important;
        }
      }
    }
    .notice-desc {
      color: #fff;
      font-size: 26rpx;
      line-height: 1.6;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      :deep(div), :deep(p), :deep(span) {
        color: #fff !important;
      }
    }
    .notice-footer {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 16rpx;
      .notice-time {
        color: #fff;
        font-size: 24rpx;
      }
    }
  }
  .notice-right {
    padding-top: 8rpx;
    margin-left: 20rpx;
  }
}
.load-more {
  text-align: center;
  padding: 30rpx 0;
  .load-text {
    color: #fff;
    font-size: 26rpx;
  }
}
</style>
