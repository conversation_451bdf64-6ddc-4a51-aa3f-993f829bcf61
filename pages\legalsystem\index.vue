<template>
  <view class="legal-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="back" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">平台介绍</text>
      </view>
    </view>
    <!-- 内容区域 -->
    <view class="content-box">
      <!-- 列表项 -->
      <view class="list-item" 
        v-for="(item, index) in agreementList" 
        :key="index" 
        @click="goToDetail(item)"
      >
        <text class="item-text">{{ item.title }}</text>
        <uni-icons type="right" size="16" color="#fff"></uni-icons>
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      statusBarHeight: 0,
      agreementList: []
    }
  },
  created() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.loadAgreementList()
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    async loadAgreementList() {
      try {
        const res = await request({
          url: '/api/agreement/list',
          method: 'GET'
        })
        if (res.code === 200 && res.data) {
          this.agreementList = res.data
        }
      } catch (e) {
        console.error('获取条款列表失败:', e)
      }
    },
    goToDetail(item) {
      uni.navigateTo({
        url: `/pages/agreement/index?id=${item.id}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.legal-container {
  min-height: 100vh;
  background: #121212 !important;
  // 自定义导航栏
  .custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #121212 !important;
    /* border-bottom: 1px solid #222; */
    .navbar-content {
      height: 44px;
      display: flex;
      align-items: center;
      padding: 0 30rpx;
      position: relative;
      .left-area {
        padding: 20rpx;
        margin-left: -20rpx;
        &:active {
          opacity: 0.7;
        }
      }
      .page-title {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        color: #fff;
        font-size: 30rpx;
        font-weight: 500;
      }
    }
  }
  // 内容区域
  .content-box {
    padding: calc(var(--status-bar-height) + 44px + 20rpx) 30rpx 30rpx;
    .list-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;
      background: #18191D !important;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      // border: 1px solid #FFD70022;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      &:active {
        background: #FFD70022;
        transform: scale(0.98);
      }
      .item-text {
        color: #fff;
        font-size: 28rpx;
      }
    }
  }
}
</style>
