<template>
  <view class="robot-follow-container">
    <!-- 加载状态 -->
    <view v-if="loading">
      <!-- 骨架屏整体布局优化 -->
      <view class="skeleton-card" v-for="i in 3" :key="i" style="margin-bottom: 24rpx;">
        <view class="skeleton-avatar"></view>
        <view class="skeleton-info">
          <view class="skeleton-title"></view>
          <view class="skeleton-row"></view>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <view v-else>
        <view class="copy-balance-card">
      <view class="card-balance-row-flex">
        <view class="card-balance-left">
          <view class="card-header">跟单账户余额</view>
          <view class="card-subtitle">跟单净利润</view>
          <view style="display: flex; align-items: flex-end;">
            <text class="card-balance">{{ safeTotalProfit }}</text>
            <text class="card-unit">USDT</text>
          </view>
          <view class="card-today" style="margin-top: 18rpx;">今日收益 <text class="card-today-value">{{ safeTodayProfit }}</text></view>
        </view>
        <view class="card-balance-right">
          <image class="card-arrow-img" src="@/static/gendancenter.png" mode="widthFix" @click="goToMyCopy" />
          <text class="card-center-text">我的跟单</text>
        </view>
      </view>
    </view>
        <view class="leader-list" v-if="safeLeaderList.length > 0">
          <view class="leader-card" v-for="(item, index) in safeLeaderList" :key="`leader-${item.id || item.username || index}`">
            <view class="leader-card-header">
              <image :src="getImageUrl(item.avatar)" class="leader-avatar" @error="handleImageError" />
              <view class="leader-userinfo">
                <view class="leader-name">{{ item.username || '未知用户' }}</view>
                <view class="leader-level-inline">
                  <text class="type-label">{{ item.copy_type === 0 ? '短线' : (item.copy_type === 1 ? '中线' : '长线') }}</text>
                 <text class="type-labels">交易周期 {{ Number(item.lock_time || 0) + '天' }}</text>
                </view>
              </view>
              <button
                    class="follow-btn"
                    :disabled="(isFollowing && user && user.leaderId !== item.id) || (user && user.isLeader === 1)"
                    @click="user && user.isFollowing === 1 && user.leaderId === item.id ? handleUnfollow() : handleFollow(item)"
                  >
                    {{ user && user.isFollowing === 1 && user.leaderId === item.id ? '一键取消' : '一键跟单' }}
                  </button>
            </view>
            <view class="leader-card-body-compact3"> 
              <view class="leader-row-compact3">
				  <view>
				    <view class="leader-label">储备金</view>
				    <view class="leader-value">{{ Number(item.reserve_amount).toFixed(4) || '0' }}</view>
				  </view>
                <view>
                  <view class="leader-label">最低跟单金额</view>
                  <view class="leader-value">{{ item.min_follow_amount || '0' }}</view>
                </view>
                <view>
                  <view class="leader-label">最高跟单金额</view>
                  <view class="leader-value">{{ Number(item.max_follow_amount || 0) === 0 ? '不限' : (item.max_follow_amount || '0') }}</view>
                </view>
               
              </view>
             
            </view>
          </view>
        </view>

      <!-- 空状态提示 -->
      <view v-else class="empty-state">
        <text class="empty-text">暂无带单员数据</text>
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import config from '@/config/index.js'
export default {
  data() {
    return {
      leaderList: [],
      loading: true,
      baseURL: config.apiBaseUrl,
      page: 0,
      size: 10,
      user: null,
      todayProfit: 0,
      totalProfit: 0,
    }
  },
  computed: {
    isFollowing() {
      return this.user && this.user.isFollowing === 1;
    },

    safeLeaderList() {
      if (!Array.isArray(this.leaderList)) {
        return [];
      }
      // 确保每个item都有必要的属性
      return this.leaderList.filter(item => item && typeof item === 'object');
    },

    safeTotalProfit() {
      const result = (Number(this.totalProfit) || 0).toFixed(4);
      console.log('计算 safeTotalProfit:', this.totalProfit, '->', result);
      return result;
    },

    safeTodayProfit() {
      const result = (Number(this.todayProfit) || 0).toFixed(4);
      console.log('计算 safeTodayProfit:', this.todayProfit, '->', result);
      return result;
    }
  },
  onShow() {
    // 确保DOM稳定后再初始化
    this.$nextTick(() => {
      this.initPage();
    });
  },

  onLoad() {
    this.initPage();
  },

  onUnload() {
    // 清理资源
    this.leaderList = [];
    this.user = null;
    this.loading = false;
  },
  methods: {
    async initPage() {
      try {
        this.loading = true;
        await Promise.all([
          this.loadUserInfo(),
          this.loadLeaderList(),
          this.loadProfitSummary()
        ]);

        // 确保DOM更新完成
        await this.$nextTick();
      } catch (e) {
        console.error('页面初始化失败:', e);
        uni.showToast({
          title: '页面加载失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        // 再次确保DOM更新
        await this.$nextTick();
      }
    },

    async loadUserInfo() {
      try {
        const res = await request({ url: '/api/user/info', method: 'GET' });
        if (res && res.data) {
          this.user = res.data;
        }
      } catch (e) {
        this.user = null;
      }
    },
    getImageUrl(imageUrl) {
      if (!imageUrl) return '/static/default-avatar.png'; // 提供默认头像
      try {
        if (imageUrl.startsWith('/static/')) {
          return imageUrl;
        }
        if (imageUrl.startsWith('/')) {
          return `${this.baseURL}${imageUrl}`;
        }
        return imageUrl;
      } catch (e) {
        console.error('获取图片URL失败:', e);
        return '/static/default-avatar.png';
      }
    },

    handleImageError(e) {
      console.log('图片加载失败:', e);
      // 可以设置默认图片
      if (e.target) {
        e.target.src = '/static/default-avatar.png';
      }
    },

    async loadLeaderList() {
      try {
        // 调用新的带单员广场接口
        const res = await request({ url: '/api/leader/summary', method: 'GET' });
        if (res && Array.isArray(res.data)) {
          // 字段适配：后端返回的字段直接赋值即可
          this.leaderList = res.data;
          await this.$nextTick(); // 确保DOM更新
        } else {
          this.leaderList = [];
        }
      } catch (e) {
        console.error('加载带单员列表失败:', e);
        this.leaderList = [];
        throw e; // 重新抛出错误，让initPage处理
      }
    },
    async loadProfitSummary() {
      try {
        console.log('开始加载收益汇总数据...');
        const res = await request({ url: '/api/copy/profit/summary', method: 'GET' });
        console.log('收益汇总接口响应:', res);

        // 更宽松的条件判断，支持 code 为 200 或 0
        if (res && (res.code === 0 || res.code === 200) && res.data) {
          this.todayProfit = res.data.today_profit || 0;
          this.totalProfit = res.data.total_profit || 0;
          console.log('收益数据更新成功:', {
            todayProfit: this.todayProfit,
            totalProfit: this.totalProfit
          });
        } else {
          console.warn('收益数据格式异常:', res);
          this.todayProfit = 0;
          this.totalProfit = 0;
        }
      } catch (e) {
        console.error('加载收益汇总失败:', e);
        this.todayProfit = 0;
        this.totalProfit = 0;
      }
    },

    // 临时添加的刷新方法用于调试
    async refreshProfitData() {
      console.log('手动刷新收益数据...');
      await this.loadProfitSummary();
      uni.showToast({
        title: '数据已刷新',
        icon: 'success'
      });
    },

    handleFollow(item) {
      // 前端校验：余额必须大于等于最小跟单金额，且（如果最大不为0）小于等于最大跟单金额
      const min = Number(item.min_follow_amount);
      const max = Number(item.max_follow_amount);
      const balance = Number(this.user && this.user.copyTradeBalance);
      if (isNaN(balance)) {
        uni.showToast({ title: '无法获取跟单账户余额', icon: 'none' });
        return;
      }
      if (balance < min) {
        uni.showToast({ title: '跟单账户余额低于最低跟单金额', icon: 'none' });
        return;
      }
      if (max > 0 && balance > max) {
        uni.showToast({ title: '跟单账户余额高于最高跟单金额', icon: 'none' });
        return;
      }
      uni.showModal({
        title: '提示',
        content: '是否确认一键跟单？',
        confirmText: '确认',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 调用后端接口，修改当前用户is_following=1，leader_id为item.id
            request({
              url: '/api/leader/follow',
              method: 'POST',
              data: {
                leaderId: item.id
              }
            }).then(async () => {
              uni.showToast({ title: '跟单成功', icon: 'success' });
              await this.loadUserInfo(); // 跟单成功后刷新用户信息
              await this.loadLeaderList();
              await this.$nextTick(); // 确保DOM更新
            }).catch(err => {
              uni.showToast({ title: err.message || '操作失败', icon: 'none' });
            });
          }
        }
      });
    },
    handleUnfollow() {
      uni.showModal({
        title: '提示',
        content: '是否确认取消跟单？',
        confirmText: '确认',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            request({
              url: '/api/leader/unfollow',
              method: 'POST'
            }).then(async () => {
              uni.showToast({ title: '已取消跟单', icon: 'success' });
              await this.loadUserInfo();
              await this.loadLeaderList();
              await this.$nextTick(); // 确保DOM更新
            }).catch(err => {
              uni.showToast({ title: err.message || '操作失败', icon: 'none' });
            });
          }
        }
      });
    },
    goToMyCopy() {
      uni.navigateTo({ url: '/pages/copy/mycopy' });
    },
  }
}
</script>

<style scoped>
.copy-balance-card {
  background: #18191D;
  border-radius: 20rpx;
  padding: 32rpx 32rpx 24rpx 32rpx;
  margin-bottom: 8rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.12);
}
.card-header {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.card-subtitle {
  color: #bbb;
  font-size: 22rpx;
  margin-bottom: 2rpx;
  display: flex;
  align-items: center;
}
.eye-icon {
  font-size: 22rpx;
  margin-left: 8rpx;
}
.card-balance-row {
  display: flex;
  align-items: flex-end;
  margin-bottom: 12rpx;
}
.card-balance {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  margin-right: 8rpx;
}
.card-unit {
  color: #fff;
  font-size: 22rpx;
  margin-right: 12rpx;
}
.card-arrow {
  color: #fff;
  font-size: 32rpx;
  margin-left: auto;
}
.card-arrow-img {
  width: 60rpx;
  height: 36rpx;
  display: inline-block;
  vertical-align: middle;
}
.card-center-text {
  color: #fff;
  font-size: 24rpx;
  text-align: center;
}
.card-today {
  color: #bbb;
  font-size: 20rpx;
}
.card-today-value {
  color: #00e676;
  font-size: 20rpx;
  margin-left: 6rpx;
}
.robot-follow-container {
  /* min-height: calc(100vh - 100rpx); */
  overflow-y: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #111;
  padding: 24rpx;
  box-sizing: border-box;
}
.leader-list {
  margin-top: 0;
}
.leader-card {
  background: #181818;
  border-radius: 18rpx;
  margin-bottom: 24rpx;
  padding: 20rpx 24rpx 18rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(255,215,0,0.08);
}
.leader-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}
/* 头像尺寸调大 */
.leader-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  border: 2rpx solid #FFD700;
  background: #222;
}
.leader-userinfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.leader-name-row {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}
.leader-name {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 2rpx;
}
.leader-meta-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.leader-level {
  color: #fff;
  font-size: 20rpx;
 
  border-radius: 8rpx;
  padding: 2rpx 8rpx;
  display: inline-block;
}
.leader-followers {
  color: #fff;
  font-size: 20rpx;
  display: flex;
  align-items: center;
}
.follow-btn {
  background: #fff;
  color: #181818;
  border-radius: 24rpx;
  font-size: 22rpx;
  font-weight: normal;
  padding: 4rpx 18rpx;
  border: none;
  margin-left: 16rpx;
}
.leader-card-middle2 {
  margin: 12rpx 0 0 0;
}
.config-lever-row2 {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 2rpx;
}
.config-item2 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.config-type-tag {
  color: #fff;
  background: #222;
  border-radius: 8rpx;
  padding: 2rpx 16rpx;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 2rpx;
  display: inline-block;
}
.config-label2 {
  font-size: 18rpx;
  color: #bbb;
  margin-top: 2rpx;
}
.lever-value2 {
  color: #FFD700;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 2rpx;
}
.leader-card-footer2 {
  margin-top: 18rpx;
  display: flex;
  justify-content: space-between;
  color: #fff;
  font-size: 22rpx;
  gap: 8rpx;
}
.footer-item2 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.footer-value2 {
  font-size: 28rpx;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 2rpx;
}
.footer-label2 {
  font-size: 18rpx;
  color: #bbb;
}
.leader-card-body {
  margin-top: 12rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid #333;
}
.leader-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.leader-label {
  color: #bbb;
  font-size: 24rpx;
}
.leader-value {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}
.leader-card-body-compact {
  margin-top: 12rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid #333;
}
.leader-row-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.leader-card-body-compact3 {
  margin-top: 12rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid #333;
}
.leader-row-compact3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18rpx;
}
.leader-row-compact3 > view {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.leader-row-compact3 > view:nth-child(2) {
  align-items: center;
}
.leader-row-compact3 > view:nth-child(3) {
  align-items: flex-end;
}
.center-row {
  justify-content: center;
}
/* 样式部分 */
.leader-level-inline {
  display: inline-flex;
  align-items: center;
  font-size: 28rpx;
  line-height: 1;
  height: 36rpx;
}
/* 类型标签去掉黄色背景，字体加粗，颜色深灰 */
.type-label {
  background: #fff;
  color: #181818;
  border-radius: 8rpx;
  padding: 0 14rpx;
  /* font-weight: bold; */
  margin-right: 8rpx;
  display: inline-flex;
  align-items: center;
  height: 36rpx;
  font-size: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}
.type-labels {
 
  color: #fff;
  border-radius: 8rpx;
  padding: 0 14rpx;
  /* font-weight: bold; */
  margin-right: 8rpx;
  display: inline-flex;
  align-items: center;
  height: 36rpx;
  font-size: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}
/* 跟单人数数字改为白色 */
.follower-inline {
  display: flex;
  align-items: center;
  height: 36rpx;
  background: #18191D;
  border-radius: 8rpx;
  padding: 0 12rpx;
}
.follower-icon {
  font-size: 30rpx;
  color: #8e44ad;
  margin-right: 4rpx;
  display: flex;
  align-items: center;
}
.follower-num {
  font-size: 26rpx;
  color: #fff;
  display: flex;
  align-items: center;
  line-height: 1;
  height: 30rpx;
}
.leader-row-compact3:first-of-type {
  margin-top: 18rpx;
}
.follow-btn:disabled {
  background: #eee;
  color: #aaa;
  cursor: not-allowed;
  opacity: 0.7;
}
.card-balance-row-flex {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  margin-bottom: 0;
}
.card-balance-left {
  flex: 3;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.card-balance-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}
.follower-icon-img {
  width: 30rpx;
  height: 30rpx;
  margin-right: 4rpx;
  display: flex;
  align-items: center;
}

/* 骨架屏样式 */
.skeleton-card {
  background: #181818;
  border-radius: 18rpx;
  padding: 20rpx 24rpx 18rpx 24rpx;
  display: flex;
  align-items: center;
  animation: skeleton-loading 1.5s ease-in-out infinite alternate;
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #333;
  margin-right: 16rpx;
  animation: skeleton-loading 1.5s ease-in-out infinite alternate;
}

.skeleton-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.skeleton-title {
  width: 60%;
  height: 32rpx;
  background: #333;
  border-radius: 4rpx;
  animation: skeleton-loading 1.5s ease-in-out infinite alternate;
}

.skeleton-row {
  width: 80%;
  height: 24rpx;
  background: #333;
  border-radius: 4rpx;
  animation: skeleton-loading 1.5s ease-in-out infinite alternate;
}

@keyframes skeleton-loading {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}
</style>