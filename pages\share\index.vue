<template>
  <view class="container">
    <custom-navbar title="邀请好友" :showBack="true" />
    <view class="content">
      <view class="invite-card">
        <view class="invite-header">
          <text class="invite-title"></text>
          <text class="invite-desc">探索区块链交易新机遇</text>
        </view>
        <view class="qrcode-box">
          <image v-if="qrCodeUrl" :src="qrCodeUrl" mode="aspectFit" class="qrcode-img" />
          <text v-else class="qrcode-loading">二维码生成中...</text>
        </view>
        <view class="invite-row">
          <text class="invite-label">邀请码</text>
          <text class="invite-value">{{ shareCode }}</text>
          <view class="copy-icon" @click="copyText(shareCode)"></view>
        </view>
        <view class="invite-row">
          <text class="invite-label">邀请链接</text>
          <text class="invite-link">{{ qrCodeLink }}</text>
          <view class="copy-icon" @click="copyText(qrCodeLink)"></view>
        </view>
        <button class="invite-btn" @click="shareToFriend">马上邀请好友</button>
      </view>
    <!--  <view class="invite-record" @click="goInviteRecord">
        <text>邀请记录</text>
        <uni-icons type="arrowright" size="20" color="#fff" />
      </view> -->
    </view>
  </view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar/custom-navbar.vue'
import config from '@/config/index.js'
import uQRCode from '@/utils/uqrcode.js'

export default {
  components: { CustomNavbar },
  data() {
    return {
      shareCode: '',
      qrCodeLink: '',
      qrCodeUrl: ''
    }
  },
  onShow() {
    this.getShareCode()
  },
  methods: {
    async getShareCode() {
      const userInfo = uni.getStorageSync('userInfo')
      if (userInfo) {
        this.shareCode = userInfo.shareCode || ''
        this.qrCodeLink = `https://invite.catcoinvip.com?invitecode=${this.shareCode}`
        // 生成二维码
        this.generateQrCode()
      }
    },
    async generateQrCode() {
      try {
        const result = await uQRCode.generate(this.qrCodeLink)
        this.qrCodeUrl = result.tempFilePath
      } catch (error) {
        console.error('生成二维码失败:', error)
      }
    },
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({ title: '已复制', icon: 'success' })
        }
      })
    },
    shareToFriend() {
      this.copyText(this.qrCodeLink)
    },
    goInviteRecord() {
      uni.navigateTo({ url: '/pages/share/record' })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #111111;
  display: flex;
  flex-direction: column;
}
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 42rpx;
}
.invite-card {
  margin-top: 48rpx;
  width: 100%;
  background: #181818;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 0 20rpx rgba(255,215,0,0.1);
}
.invite-header {
  text-align: center;
  margin-bottom: 64rpx;
  .invite-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 32rpx;
    display: block;
  }
  .invite-desc {
    font-size: 28rpx;
    color: #CCCCCC;
    line-height: 1.6;
    padding: 0 24rpx;
  }
}
.qrcode-box {
  width: 320rpx;
  height: 320rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 0;
  margin-bottom: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  .qrcode-img {
    width: 320rpx;
    height: 320rpx;
    border-radius: 20rpx;
  }
  .qrcode-loading {
    color: #666666;
    font-size: 28rpx;
  }
}
.invite-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  width: 100%;
  background: #232323;
  padding: 28rpx 24rpx;
  border-radius: 16rpx;
  &:last-of-type {
    margin-bottom: 0;
  }
  .invite-label {
    color: #fff;
    font-size: 28rpx;
    width: 120rpx;
  }
  .invite-value, .invite-link {
    color: #fff;
    font-size: 28rpx;
    flex: 1;
    margin-right: 24rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .invite-link {
    color: #fff;
  }
  .copy-icon {
    width: 40rpx;
    height: 40rpx;
    position: relative;
    cursor: pointer;
    &::before, &::after {
      content: '';
      position: absolute;
      width: 26rpx;
      height: 26rpx;
      border-radius: 6rpx;
      background: #fff;
    }
    &::before {
      top: 4rpx;
      left: 4rpx;
      opacity: 0.3;
    }
    &::after {
      bottom: 4rpx;
      right: 4rpx;
    }
  }
}
.invite-btn {
  width: 100%;
  height: 96rpx;
  margin-top: 48rpx;
  background: linear-gradient(90deg, #FFD700 0%, #FDB931 100%);
  color: #111111;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.invite-record {
  width: 100%;
  margin-top: 32rpx;
  background: #181818;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  color: #fff;
  font-size: 28rpx;
  box-shadow: 0 0 20rpx rgba(255,215,0,0.1);
  border: 1px solid rgba(255,215,0,0.1);
}
</style> 