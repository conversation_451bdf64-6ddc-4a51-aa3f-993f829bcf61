import{H as a,n as t,e,f as s,h as i,w as o,i as l,o as r,j as n,m as c,z as d,l as u,q as m,F as f,x as p,t as g}from"./index-BnjgV7rC.js";import{_ as h}from"./uni-icons.9b9P_X39.js";import{r as _}from"./uni-app.es.BuhgQoIe.js";import{r as x}from"./request.CNAVEKUC.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const y=k({data:()=>({statusBarHeight:0,agreementList:[]}),created(){const t=a();this.statusBarHeight=t.statusBarHeight,this.loadAgreementList()},methods:{handleBack(){t()},async loadAgreementList(){try{const a=await x({url:"/api/agreement/list",method:"GET"});200===a.code&&a.data&&(this.agreementList=a.data)}catch(a){console.error("获取条款列表失败:",a)}},goToDetail(a){e({url:`/pages/agreement/index?id=${a.id}`})}}},[["render",function(a,t,e,x,k,y){const j=_(s("uni-icons"),h),B=l,b=p;return r(),i(B,{class:"legal-container"},{default:o((()=>[n(B,{class:"custom-navbar",style:d({paddingTop:k.statusBarHeight+"px"})},{default:o((()=>[n(B,{class:"navbar-content"},{default:o((()=>[n(B,{class:"left-area",onClick:y.handleBack},{default:o((()=>[n(j,{type:"back",size:"20",color:"#fff"})])),_:1},8,["onClick"]),n(b,{class:"page-title"},{default:o((()=>[c("平台介绍")])),_:1})])),_:1})])),_:1},8,["style"]),n(B,{class:"content-box"},{default:o((()=>[(r(!0),u(f,null,m(k.agreementList,((a,t)=>(r(),i(B,{class:"list-item",key:t,onClick:t=>y.goToDetail(a)},{default:o((()=>[n(b,{class:"item-text"},{default:o((()=>[c(g(a.title),1)])),_:2},1024),n(j,{type:"right",size:"16",color:"#fff"})])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})}],["__scopeId","data-v-44553b55"]]);export{y as default};
