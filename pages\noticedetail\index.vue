<template>
  <view class="notice-detail">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">公告详情</text>
        <view class="right-area"></view>
      </view>
    </view>
    <!-- 公告内容区域 -->
    <scroll-view 
      class="detail-content"
      scroll-y
      :style="{ height: scrollHeight + 'px' }"
    >
      <view class="notice-header">
        <view class="title-wrap">
          <text class="tag" :class="{ important: noticeDetail.noticeType === 1 || noticeDetail.isTop === 1 }">
            {{ getNoticeTypeText(noticeDetail.noticeType) }}
          </text>
          <text class="title">{{ noticeDetail.title }}</text>
        </view>
        <text class="time">{{ formatTime(noticeDetail.createTime) }}</text>
      </view>
      <!-- 富文本内容 -->
      <view class="rich-content">
        <rich-text :nodes="formatRichText(noticeDetail.content)"></rich-text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      statusBarHeight: 0,
      scrollHeight: 0,
      noticeId: '',
      noticeDetail: {}
    }
  },
  onLoad(options) {
    if (options.id) {
      this.noticeId = options.id
      this.getNoticeDetail()
    }
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.scrollHeight = systemInfo.windowHeight
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    async getNoticeDetail() {
      try {
        const res = await request({
          url: `/api/notice/detail/${this.noticeId}`,
          method: 'GET'
        })
        if (res.code === 200 && res.data) {
          this.noticeDetail = res.data
        }
      } catch (e) {
        console.error('获取公告详情失败:', e)
      }
    },
    formatRichText(html) {
      if (!html) return ''
      html = html.replace(/<img/gi, '<img style="max-width:100%;height:auto;display:block;"')
      html = html.replace(/<video/gi, '<video style="max-width:100%;height:auto;display:block;"')
      html = html.replace(/color:\s*rgb\(0,\s*0,\s*0\)/gi, 'color: #fff')
      html = html.replace(/color:\s*#000000/gi, 'color: #fff')
      html = html.replace(/color:\s*black/gi, 'color: #fff')
      html = html.replace(/<p>/gi, '<p style="color: #fff;">')
      html = html.replace(/<span>/gi, '<span style="color: #fff;">')
      return html
    },
    formatTime(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    getNoticeTypeText(type) {
      switch(type) {
        case 1: return '重要';
        case 2: return '通知';
        case 3: return '系统';
        case 4: return '活动';
        case 5: return '维护';
        default: return '通知';
      }
    }
  }
}
</script>

<style lang="scss">
.notice-detail {
  min-height: 100vh;
  background: #121212 !important;
  .custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #121212 !important;
    .navbar-content {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;
      .left-area {
        width: 80rpx;
        height: 44px;
        display: flex;
        align-items: center;
        &:active {
          opacity: 0.7;
        }
      }
      .page-title {
        color: #fff;
        font-size: 30rpx;
        font-weight: 500;
      }
      .right-area {
        width: 80rpx;
      }
    }
  }
  .detail-content {
    box-sizing: border-box;
    padding: calc(var(--status-bar-height) + 44px + 20rpx) 30rpx 30rpx;
  }
  .notice-header {
    margin-bottom: 30rpx;
    .title-wrap {
      margin-bottom: 20rpx;
      display: flex;
      align-items: flex-start;
      .tag {
        font-size: 22rpx;
        padding: 4rpx 12rpx;
        border-radius: 6rpx;
        background: #FFD70022;
        color: #FFD70099;
        margin-right: 12rpx;
        &.important {
          background: #F34A69 !important;
          color: #fff !important;
        }
      }
      .title {
        flex: 1;
        color: #fff;
        font-size: 28rpx;
        font-weight: 500;
        line-height: 1.4;
      }
    }
    .time {
      color: #fff;
      font-size: 24rpx;
    }
  }
  .rich-content {
    background: #18191D !important;
    border-radius: 16rpx;
    padding: 30rpx;
    color: #fff;
    font-size: 28rpx;
    line-height: 1.6;
    :deep(img) {
      max-width: 100%;
      height: auto;
    }
    :deep(p) {
      margin: 16rpx 0;
    }
    :deep(video) {
      width: 100%;
      margin: 16rpx 0;
    }
  }
}
</style>
