<template>
  <view class="record-container" :style="{ paddingTop: navPaddingTop + 'px' }">
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">划转记录</text>
        <view class="right-area"></view>
      </view>
    </view>
    
    <!-- 记录列表 -->
    <view class="record-list" v-if="transferRecords.length > 0">
      <view 
        class="record-item" 
        v-for="(record, index) in transferRecords" 
        :key="index"
      >
        <view class="record-main">
          <view class="record-left">
            <text class="transfer-direction">{{ getAccountTypeText(record.fromAccountType) }} -> {{ getAccountTypeText(record.toAccountType) }}</text>
            <text class="record-time">{{ formatTime(record.createTime) }}</text>
          </view>
          <view class="record-right">
            <text class="amount-value">{{ record.amount }} USDT</text>
            <text class="record-status" :class="getStatusClass(record.status)">{{ getStatusText(record.status) }}</text>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore" @click="loadMore">
        <text class="load-text">加载更多</text>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <view class="empty-img">
        <svg width="100" height="100" viewBox="0 0 180 180">
          <rect x="40" y="80" width="100" height="60" rx="8" fill="#d6ff3c" />
          <rect x="60" y="60" width="60" height="40" rx="8" fill="#d6ff3c" opacity="0.7"/>
        </svg>
      </view>
      <text class="empty-text">暂无划转记录</text>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      statusBarHeight: 0,
      navBarHeight: 44, // px
      navPaddingTop: 44, // 默认导航栏高度
      transferRecords: [],
      page: 1,
      pageSize: 10,
      hasMore: true,
      isLoading: false
    }
  },
  created() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.navPaddingTop = this.statusBarHeight + this.navBarHeight
  },
  onShow() {
    this.loadTransferRecords(true)
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    
    // 加载划转记录
    async loadTransferRecords(isRefresh = false) {
      if (this.isLoading) return
      
      if (isRefresh) {
        this.page = 1
        this.hasMore = true
        this.transferRecords = []
      }
      
      if (!this.hasMore && !isRefresh) return
      
      try {
        this.isLoading = true
        const token = uni.getStorageSync('token')
        const res = await request({
          url: '/api/transfer/records',
          method: 'GET',
          params: {
            page: this.page,
            size: this.pageSize
          },
          header: {
            'Authorization': token ? `Bearer ${token}` : ''
          }
        })
        
        if (res && res.code === 200) {
          const records = res.data?.records || []
          
          if (isRefresh) {
            this.transferRecords = records
          } else {
            this.transferRecords.push(...records)
          }
          
          this.hasMore = records.length === this.pageSize
          if (this.hasMore) {
            this.page++
          }
        } else {
          uni.showToast({
            title: res?.msg || '获取记录失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取划转记录失败:', error)
        uni.showToast({
          title: '获取记录失败',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
      }
    },
    
    // 加载更多
    loadMore() {
      if (!this.isLoading && this.hasMore) {
        this.loadTransferRecords()
      }
    },
    
    // 获取划转类型文本
    getTransferTypeText(record) {
      return `${record.fromAccountType} -> ${record.toAccountType}`
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '处理中',
        1: '成功',
        2: '失败'
      }
      return statusMap[status] || '未知'
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case 0: return 'status-pending'
        case 1: return 'status-success'
        case 2: return 'status-fail'
        default: return 'status-default'
      }
    },
    
    // 获取账户类型文本
    getAccountTypeText(accountType) {
      const typeMap = {
        'fund': '资金账户',
        'commission': '佣金账户',
        'copy': '跟单账户',
        'profit': '利润账户'
      }
      return typeMap[accountType] || accountType
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.record-container {
  min-height: 100vh;
  background: #121212 !important;
  padding-bottom: 40rpx;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #121212 !important;
}

.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  width: 100%;
  box-sizing: border-box;
}

.left-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
}

.right-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.page-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 30rpx !important;
  font-weight: 500;
}

.record-list {
  padding: 20rpx 30rpx;
}

.record-item {
  background: #18191D !important;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border: 1px solid rgba(255,215,0,0.12);
  box-shadow: 0 2rpx 8rpx rgba(255,215,0,0.08);
}

.record-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-left {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.transfer-direction {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}

.record-time {
  color: #666;
  font-size: 24rpx;
}

.record-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.amount-value {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
}

.record-status {
  font-size: 24rpx;
  padding: 2rpx 12rpx;
  border-radius: 10rpx;
  
  &.status-pending {
    background: rgba(255, 193, 7, 0.1);
    color: #FFC107;
  }
  
  &.status-success {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
  }
  
  &.status-fail {
    background: rgba(244, 67, 54, 0.1);
    color: #F44336;
  }
}

.load-more {
  text-align: center;
  padding: 20rpx 0;
  .load-text {
    color: #fff;
    font-size: 28rpx;
    opacity: 0.8;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-img {
  margin-bottom: 40rpx;
}

.empty-text {
  color: #888;
  font-size: 24rpx;
}
</style> 