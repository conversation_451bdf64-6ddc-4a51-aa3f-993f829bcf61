<template>
  <view class="password-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">修改密码</text>
        <view class="right-area"></view>
      </view>
    </view>
    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 当前手机号 -->
      <view class="phone-info">
        <text class="label">当前绑定邮箱：</text>
        <text class="phone">{{ maskEmail(userInfo.email) }}</text>
      </view>
      <!-- 表单项 -->
      <view class="form-item">
        <text class="form-label">登录密码</text>
        <input 
          class="form-input" 
          type="password" 
          v-model="password"
          placeholder="请输入新登录密码"
          placeholder-style="color: #695a5a;"
        />
      </view>
      <view class="form-item">
        <text class="form-label">验证码</text>
        <view class="verify-group">
          <input 
            class="form-input" 
            type="text" 
            v-model="verifyCode"
            placeholder="验证码"
            placeholder-style="color: #695a5a;"  
          />
          <button 
            class="verify-btn" 
            :disabled="counting > 0 || isCodeSending"
            @click="sendVerifyCode"
          >
            <template v-if="isCodeSending">发送中...</template>
            <template v-else-if="counting > 0">{{ counting }}s</template>
            <template v-else>发送验证码</template>
          </button>
        </view>
      </view>
      <!-- 提交按钮 -->
      <button class="submit-btn" @click="handleSubmit">确定修改</button>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      statusBarHeight: 0,
      password: '',
      verifyCode: '',
      counting: 0,
      timer: null,
      userInfo: {},
      isCodeSending: false
    }
  },
  created() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  onShow() {
    this.getUserInfo()
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    async getUserInfo() {
      try {
        const res = await request({
          url: '/api/user/info',
          method: 'GET'
        })
        if (res.code === 200) {
          this.userInfo = res.data
        }
      } catch (error) {
        console.error('获取用户信息失败', error)
      }
    },
    async sendVerifyCode() {
      if (this.counting > 0 || this.isCodeSending) return
      this.isCodeSending = true
      try {
        const res = await request({
          url: '/api/auth/send-reset-code-email',
          method: 'POST',
          params: { email: this.userInfo.email }
        })
        if (res.code === 200) {
          uni.showToast({
            title: '验证码已发送',
            icon: 'none'
          })
          // 开始倒计时
          this.counting = 60
          if (this.timer) clearInterval(this.timer)
          this.timer = setInterval(() => {
            if (this.counting > 0) {
              this.counting--
            } else {
              clearInterval(this.timer)
              this.timer = null
            }
          }, 1000)
        } else {
          uni.showToast({
            title: res.message || '发送失败',
            icon: 'none'
          })
        }
      } catch (e) {
        console.error('发送验证码失败:', e)
        uni.showToast({
          title: '发送失败',
          icon: 'none'
        })
      } finally {
        this.isCodeSending = false
      }
    },
    async handleSubmit() {
      if (!this.password) {
        uni.showToast({
          title: '请输入新密码',
          icon: 'none'
        })
        return
      }
      if (!this.verifyCode) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        })
        return
      }
      try {
        uni.showLoading({
          title: '提交中...'
        })
        const res = await request({
          url: '/api/user/password/update',
          method: 'POST',
          data: {
            password: this.password,
            verifyCode: this.verifyCode
          }
        })
        if (res.code === 200) {
          uni.showToast({
            title: '密码修改成功',
            icon: 'success'
          })
          setTimeout(() => {
            uni.clearStorage()
            uni.reLaunch({
              url: '/pages/login/index'
            })
          }, 1500)
        } else {
          uni.showToast({
            title: res.message || '修改失败',
            icon: 'none'
          })
        }
      } catch (e) {
       
        uni.showToast({
          title: e.message || '修改失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },
    maskEmail(email) {
      if (!email) return '';
      const [name, domain] = email.split('@');
      if (!domain) return email;
      if (name.length <= 2) {
        return '*'.repeat(name.length) + '@' + domain;
      }
      return name[0] + '*'.repeat(name.length - 2) + name[name.length - 1] + '@' + domain;
    }
  }
}
</script>
<style lang="scss" scoped>
.password-container {
  min-height: 100vh;
  background: #121212 !important;
  .custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #121212 !important;
    .navbar-content {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;
      .left-area {
        width: 80rpx;
        height: 44px;
        display: flex;
        align-items: center;
        &:active {
          opacity: 0.7;
        }
      }
      .page-title {
        color: #fff;
        font-size: 30rpx;
        font-weight: 500;
      }
      .right-area {
        width: 80rpx;
      }
    }
  }
  .form-content {
    padding: calc(var(--status-bar-height) + 88px) 30rpx 40rpx;
    .phone-info {
      margin-bottom: 40rpx;
      display: flex;
      align-items: center;
      .label {
        color: #fff;
        font-size: 28rpx;
      }
      .phone {
        color: #fff;
        font-size: 28rpx;
        margin-left: 10rpx;
      }
    }
    .form-item {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;
      .form-label {
        color: #fff;
        font-size: 28rpx;
        width: 140rpx;
      }
      .form-input {
        flex: 1;
        height: 56rpx;
        background: #1E1E1E !important;
        border-radius: 8rpx;
        padding: 0 20rpx;
        color: #fff;
        font-size: 24rpx;
        border: none;
      }
      .verify-group {
        flex: 1;
        display: flex;
        gap: 20rpx;
        .form-input {
          flex: 1;
        }
        .verify-btn {
          min-width: 100rpx;
          width: auto;
          height: 56rpx;
          line-height: 56rpx;
          border-radius: 8rpx;
          font-size: 24rpx;
          color: #18191D !important;
          background: #fff !important;
          border: none;
          margin-left: 12rpx;
          margin-right: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
          padding: 0 18rpx;
          font-weight: normal;
          letter-spacing: 2rpx;
          transition: background 0.2s, color 0.2s;
          &:active {
            opacity: 0.8;
            background: #eee !important;
            color: #18191D !important;
          }
          &:disabled {
            opacity: 0.6;
            color: rgba(24, 25, 29, 0.5) !important;
            background: #fff !important;
          }
        }
      }
    }
    .submit-btn {
      width: 100%;
      height: 90rpx;
      line-height: 90rpx;
      background: #fff !important;
      border-radius: 8rpx;
      color: #18191D !important;
      font-size: 28rpx;
      margin-top: 60rpx;
      font-weight: 500;
      border: none;
      box-shadow: 0 2rpx 8rpx #FFD70044;
      transition: background 0.2s, color 0.2s;
      &:active {
        opacity: 0.8;
        background: #eee !important;
        color: #18191D !important;
      }
    }
  }
}
</style>

