import{H as a,a4 as s,n as t,f as e,o,h as i,w as n,j as r,k as l,m as c,t as u,z as p,i as d,x as h}from"./index-BnjgV7rC.js";import{_ as f}from"./uni-icons.9b9P_X39.js";import{r as m}from"./uni-app.es.BuhgQoIe.js";import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const B=g({name:"CustomNavbar",props:{title:{type:String,default:""},showBack:{type:Boolean,default:!1}},data:()=>({statusBarHeight:20,navbarHeight:64}),mounted(){const s=a();this.statusBarHeight=s.statusBarHeight,this.navbarHeight=this.statusBarHeight+44},methods:{handleBack(){s(),t()}}},[["render",function(a,s,t,g,B,_){const k=m(e("uni-icons"),f),H=d,v=h;return o(),i(H,{class:"custom-navbar",style:p({paddingTop:B.statusBarHeight+"px"})},{default:n((()=>[r(H,{class:"navbar-content"},{default:n((()=>[t.showBack?(o(),i(H,{key:0,class:"left-area",onClick:_.handleBack},{default:n((()=>[r(k,{type:"back",size:"20",color:"#fff"})])),_:1},8,["onClick"])):l("",!0),r(v,{class:"title"},{default:n((()=>[c(u(t.title),1)])),_:1})])),_:1})])),_:1},8,["style"])}],["__scopeId","data-v-57536a0e"]]);export{B as C};
