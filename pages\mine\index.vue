<template>
  <view class="container">
    <!-- <custom-navbar title="我的" :show-back="false" /> -->
    
    <!-- 用户信息区域 -->
    <view class="user-info glass-effect animate-item">
      <image class="avatar" :src="avatarUrl" @click="goToUploadAvatar" @error="onAvatarError" />
      <view class="user-details">
        <text class="account">账号：{{ maskEmail(userEmail) }}</text>
        <text class="uid">UID：{{ userNo }}</text>
        <text class="invite-code">CAT：{{ Number(catBalance).toFixed(4) }}</text>
		<text class="invite-code" v-if="commissionRate>0 && isActivated">佣金比例：{{ commissionRate }}%</text>
      </view>
    </view>

    <!-- 资产统计卡片 -->
   <view class="stats-card glass-effect animate-item">
    <!-- <view class="stats-header">
      <text class="stats-title">我的资产</text>
    </view> -->
    <view class="stats-btn-row">
      <button class="recharge-btn" @click="handleRecharge">充值</button>
      <button class="withdraw-btn" style="z-index: 999;" @click="handleWithdraw">提现</button>
      <button class="transfer-btn" @click="handleTransfer">划转</button>
    </view>

    <view class="account-grid">
      <view class="account-row">
        <view class="account-card" @click="goToAsset">
          <text class="account-name">资金账户(USDT)</text>
          <text class="account-balance">{{ (Number(availableBalance)).toFixed(4) }} </text>
        </view>
        <view class="account-card">
          <text class="account-name">跟单账户(USDT)</text>
          <text class="account-balance">{{ (Number(copyTradeBalance)).toFixed(4) }} </text>
        </view>
      </view>
      <view class="account-row">
        <view class="account-card">
          <text class="account-name">佣金账户(USDT)</text>  
          <text class="account-balance">{{ Number(commissionBalance).toFixed(4) }} </text>
        </view>
        <view class="account-card">
          <text class="account-name">利润账户(USDT)</text>
          <text class="account-balance">{{ (Number(profitBalance)).toFixed(4) }} </text>
        </view>
      </view>
    </view>
    <view class="stats-summary">
      <view class="summary-item main">
        <text class="summary-label">今日收益(USDT)</text>
        <text class="summary-value">{{ statsLoading ? '--' : ( + Number(todayCommission).toFixed(4)) }}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">买涨(次)</text>
        <text class="summary-value">{{ statsLoading ? '--' : upCount }}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">买跌(次)</text>
        <text class="summary-value">{{ statsLoading ? '--' : downCount }}</text>
      </view>
    </view>
   </view>
    
    <!-- 资产统计卡片结束 -->
    <view class="asset-extra-card">
      <view class="extra-item team" @click="handleTeam">
        <view class="extra-icon team-icon">
          <image src="/static/tools/team.png" mode="aspectFit" />
        </view>
        <text class="extra-label">我的团队</text>
      </view>
      <view class="extra-item profit" @click="goToProfit">
        <view class="extra-icon profit-icon">
          <image src="/static/tools/shouyidetails.png" mode="aspectFit" />
        </view>
        <view class="extra-info">
          <text class="extra-label">佣金/收益明细</text>
          <!-- <text class="extra-value">$0</text> -->
        </view>
      </view>
    </view>

    <!-- 带单员中心独立卡片 -->
    <view v-if="isLeader" class="leader-center-card" @click="goToLeaderCenter">
      <view class="leader-content">
        <view class="leader-icon-wrapper">
          <image src="/static/daidan.png" mode="aspectFit" class="leader-icon" />
        </view>
        <view class="leader-info">
          <text class="leader-title">带单员中心</text>
          <text class="leader-desc">管理您的带单业务</text>
        </view>
        <image src="/static/gold.png" mode="aspectFit" class="leader-gold-img" />
      </view>
    </view>

    <!-- 分隔线 -->
    <view v-if="isLeader" class="section-divider"></view>

    <!-- 邀请横幅 -->
    <view class="invite-banner" @click="handleInvite">
      <view class="invite-content">
        <view class="invite-icon-wrapper">
          <image src="/static/yaoqing.png" mode="aspectFit" class="invite-icon" />
        </view>
        <view class="invite-text">
          <text class="invite-title">人人有礼</text>
          <text class="invite-desc">邀请好友得奖励</text>
        </view>
      </view>
      <image class="invite-img" src="/static/lipin.png" mode="aspectFit" />
    </view>
    
    
    <!-- 添加划转弹窗 -->
    <view class="transfer-popup" v-if="showTransferPopup">
      <view class="popup-content" @click.stop>
        <view class="popup-title">账户划转</view>
        <!-- 自定义下拉选择：从账户 -->
        <view class="custom-select" @click="toggleFromDropdown">
          <view class="picker-label">从账户</view>
          <view class="picker-value">{{ accountList[transferFrom]?.name || '请选择' }}</view>
          <view class="arrow">▼</view>
          <view class="dropdown-list" v-if="showFromDropdown">
            <view 
              class="dropdown-item" 
              v-for="(item, idx) in accountList" 
              :key="idx" 
              @click.stop="selectFrom(idx)"
            >{{ item.name }}</view>
          </view>
        </view>
        <!-- 自定义下拉选择：到账户 -->
        <view class="custom-select" @click="toggleToDropdown">
          <view class="picker-label">到账户</view>
          <view class="picker-value">{{ accountList[transferTo]?.name || '请选择' }}</view>
          <view class="arrow">▼</view>
          <view class="dropdown-list" v-if="showToDropdown">
            <view 
              class="dropdown-item" 
              v-for="(item, idx) in availableToAccounts" 
              :key="idx" 
              @click.stop="selectTo(accountList.findIndex(acc => acc.type === item.type))"
            >{{ item.name }}</view>
          </view>
        </view>
        <!-- 余额显示 -->
        <view class="balance-row">余额：{{ getAccountBalance(transferFrom) }} USDT</view>
        <view class="input-row">
          <input class="amount-input" v-model="transferAmount" type="number" placeholder="请输入划转金额" />
        </view>
        <button class="confirm-btn" @click="confirmTransfer">确定</button>
        <view class="close-btn" @click="closeTransferPopup">×</view>
      </view>
    </view>
    <!-- 支付密码弹窗 -->
    <view class="password-popup" v-if="showPasswordPopup" @click="closePasswordPopup">
      <view class="popup-content" @click.stop>
        <view class="popup-title">请输入支付密码</view>
        <input class="password-input" v-model="payPassword" type="password" placeholder="支付密码" />
        <button class="confirm-btn" @click="submitTransfer">确认划转</button>
        <view class="close-btn" @click="closePasswordPopup">×</view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar/custom-navbar.vue'
import request from '@/utils/request.js'
import config from '@/config/index.js'

export default {
  components: {
    CustomNavbar
  },
  data() {
    return {
      userEmail: '',
      userNo: '',
      availableBalance: '0.00',
      copyTradeBalance: '0.00',
      commissionBalance: '0.00',
      usageFrozenBalance: '0.00',
      profitBalance: '0.00',
      inviteCode: '',
      userType: '',
      carbonPoints: '',
      exchangeVouchers: '',
      dynamicQuota:'',
      waitExchangeGb: '',
      catBalance: '0.00',
	  isActivated:false,
	  commissionRate:0,
      assetsMenu: [
        { icon: '/static/icons/svg/carbon.svg', text: '我的资产', type: 'carbon' },
        { icon: '/static/icons/svg/gift.svg', text: '我的团队', type: 'gift' },
        { icon: '/static/icons/svg/exchange.svg', text: '收益明细', type: 'exchange' },
        { icon: '/static/icons/svg/battery.svg', text: '人人有礼', type: 'battery' }
      ],
      accountList: [
        { name: '资金账户', type: 'fund' },
        { name: '佣金账户', type: 'commission' },
        { name: '跟单账户', type: 'copy' },
        { name: '利润账户', type: 'profit' }
      ],
      showTransferPopup: false,
      showPasswordPopup: false,
      transferFrom: null,
      transferTo: null,
      transferAmount: '',
      payPassword: '',
      showFromDropdown: false,
      showToDropdown: false,
      todayCommission: null,
      upCount: null,
      downCount: null,
      statsLoading: true,
      avatarUrl: '/static/tools/default.png',
      defaultAvatar: '/static/tools/default.png',
      isLeader: false,
    }
  },
  computed: {
    // 根据源账户获取可选的目标账户列表
    availableToAccounts() {
      if (this.transferFrom === null) return this.accountList;
      
      const fromType = this.accountList[this.transferFrom].type;
      switch(fromType) {
        case 'fund': // 资金账户只能转到跟单账户
          return this.accountList.filter(acc => acc.type === 'copy');
          
        case 'copy': // 跟单账户只能转到资金账户
          return this.accountList.filter(acc => acc.type === 'fund');
          
        case 'commission': // 佣金账户可以转到资金账户或跟单账户
          return this.accountList.filter(acc => 
            acc.type === 'fund' || acc.type === 'copy'
          );
          
        case 'profit': // 利润账户可以转到资金账户或跟单账户
          return this.accountList.filter(acc => 
            acc.type === 'fund' || acc.type === 'copy'
          );
          
        default:
          return [];
      }
    }
  },
  onShow() {
    this.loadUserInfo();
    this.loadTodayStats();
  },
  methods: {
    // 格式化账号显示
    formatAccount(account) {
      if (!account || account === '') {
        return ''
      }
      if (account.length > 7) {
        return account.substr(0, 3) + '****' + account.substr(-4)
      }
      return account
    },
    maskEmail(email) {
      if (!email) return '';
      const atIdx = email.indexOf('@');
      if (atIdx <= 2) return email;
      const prefix = email.slice(0, 2);
      const last = email.slice(atIdx - 1, atIdx);
      return prefix + '****' + last + email.slice(atIdx);
    },
    getAvatarUrl(avatar) {
      if (!avatar) return '/static/tools/default.png';
      if (avatar.startsWith('http')) return avatar;
      return config.apiBaseUrl + avatar;
    },
    onAvatarError() {
      this.avatarUrl = this.defaultAvatar;
    },
    // 添加加载用户信息的方法
    async loadUserInfo() {
      try {
        // 每次都从服务器获取最新数据
        const res = await request({
          url: '/api/user/info',
          method: 'GET'
        })
        
        if (res.code === 200 && res.data) { 
		  // 更新页面显示
		  this.userEmail = res.data.email || ''
		  this.userNo = res.data.userNo || ''
		  this.availableBalance = res.data.availableBalance || '0.00'
		  this.copyTradeBalance = res.data.copyTradeBalance || '0.00'
		  this.commissionBalance = res.data.commissionBalance || '0.00'
		  this.profitBalance = res.data.profitBalance || '0.00'
		  this.inviteCode = res.data.shareCode ;
		  this.catBalance = res.data.catBalance || '0.00'
          this.avatarUrl = this.getAvatarUrl(res.data.avatar)
		  this.isActivated=res.data.isActivated;
		  this.commissionRate=res.data.commissionRate;
		  // 更新本地存储
		  uni.setStorageSync('userInfo', res.data)
		  
         if (!res.data.status) {
           uni.redirectTo({
             url: '/pages/login/index'
           })
           return
         }
         this.isLeader = res.data.isLeader === 1;
        }
      } catch (e) {
        console.error('获取用户信息失败:', e)
        // 如果获取失败，则使用本地缓存的数据
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo) {
          this.userEmail = userInfo.email || ''
          this.userNo = userInfo.userNo || ''
          this.availableBalance = userInfo.availableBalance || '0.00'
          this.copyTradeBalance = userInfo.copyTradeBalance || '0.00'
          this.commissionBalance = userInfo.commissionBalance || '0.00'
          this.profitBalance = userInfo.profitBalance || '0.00'
          this.catBalance = userInfo.catBalance || '0.00'
          this.avatarUrl = this.getAvatarUrl(userInfo.avatar)
          this.isLeader = userInfo.isLeader === 1;
        }
        
        // uni.showToast({
        //   title: '获取最新数据失败',
        //   icon: 'none'
        // })
      }
    },
    async loadTodayStats() {
      this.statsLoading = true;
      try {
        const res = await request({ url: '/api/user/today-stats', method: 'GET' });
        if (res.code === 200 && res.data) {
          this.todayCommission = res.data.todayCommission;
          this.upCount = res.data.upCount;
          this.downCount = res.data.downCount;
        } else {
          this.todayCommission = 0;
          this.upCount = 0;
          this.downCount = 0;
        }
      } catch (e) {
        this.todayCommission = 0;
        this.upCount = 0;
        this.downCount = 0;
      } finally {
        this.statsLoading = false;
      }
    },
    
    handleRecharge() {
      console.log('充值');
      uni.navigateTo({
        url: '/pages/recharge/index'
      })
    },
    handleTransfer() {
      this.showTransferPopup = true;
    },
    toggleFromDropdown() {
      this.showFromDropdown = !this.showFromDropdown;
      this.showToDropdown = false;
    },
    toggleToDropdown() {
      this.showToDropdown = !this.showToDropdown;
      this.showFromDropdown = false;
    },
    selectFrom(idx) {
      this.transferFrom = idx;
      this.transferTo = null; // 重置目标账户
      this.showFromDropdown = false;
    },
    selectTo(idx) {
      this.transferTo = idx;
      this.showToDropdown = false;
    },
    closeTransferPopup() {
      this.showTransferPopup = false;
      this.transferAmount = '';
      this.payPassword = '';
      this.showFromDropdown = false;
      this.showToDropdown = false;
      this.transferFrom = null;
      this.transferTo = null;
    },
    confirmTransfer() {
      // 1. 验证是否选择了账户
      if (this.transferFrom === null || this.transferTo === null) {
        uni.showToast({ title: '请选择划转账户', icon: 'none' });
        return;
      }
      
      // 2. 验证金额
      const amount = Number(this.transferAmount);
      if (!this.transferAmount || this.transferAmount.trim() === '') {
        uni.showToast({ title: '请输入划转金额', icon: 'none' });
        return;
      }
      
      if (isNaN(amount) || amount <= 0) {
        uni.showToast({ title: '请输入有效金额（大于0）', icon: 'none' });
        return;
      }
      
      // 3. 验证余额
      const fromBalance = Number(this.getAccountBalance(this.transferFrom));
      if (amount > fromBalance) {
        uni.showToast({ title: '余额不足', icon: 'none' });
        return;
      }
      
      // 4. 验证通过，显示支付密码弹窗
      this.showPasswordPopup = true;
    },
    
    // 验证划转规则
    validateTransferRule(fromType, toType) {
      // 资金账户 ↔ 跟单账户（双向）
      if ((fromType === 'fund' && toType === 'copy') || 
          (fromType === 'copy' && toType === 'fund')) {
        return true;
      }
      
      // 佣金账户 → 资金账户或跟单账户
      if (fromType === 'commission' && (toType === 'fund' || toType === 'copy')) {
        return true;
      }
      
      // 利润账户 → 资金账户或跟单账户
      if (fromType === 'profit' && (toType === 'fund' || toType === 'copy')) {
        return true;
      }
      
      // 其他情况都不允许
      return false;
    },
    closePasswordPopup() {
      this.showPasswordPopup = false;
      this.payPassword = '';
    },
    submitTransfer() {
      if (!this.payPassword) {
        uni.showToast({ 
          title: '请输入支付密码', 
          icon: 'none',
          duration: 2000,
          mask: true  // 添加遮罩防止点击穿透
        });
        return;
      }
      
      // 显示加载提示
      uni.showLoading({ 
        title: '划转中...',
        mask: true  // 添加遮罩防止点击穿透
      });
      
      // 构建请求参数
      const transferData = {
        fromAccountType: this.accountList[this.transferFrom].type,
        toAccountType: this.accountList[this.transferTo].type,
        amount: Number(this.transferAmount),
        payPassword: this.payPassword
      };
      
      // 调用UserController的划转API
      request({
        url: '/api/user/transfer',
        method: 'POST',
        data: transferData
      }).then(res => {
        uni.hideLoading();
        if (res.code === 200) {
          // 划转成功
          uni.showToast({ 
            title: '划转成功', 
            icon: 'success',
            duration: 2000,
            mask: true
          });
          // 先显示提示，延迟关闭弹窗
          setTimeout(() => {
            this.showPasswordPopup = false;
            this.showTransferPopup = false;
            this.transferAmount = '';
            this.payPassword = '';
            // 重新加载用户信息
            this.loadUserInfo();
          }, 1500);
        } else {
          // 划转失败，显示后端返回的错误信息
          uni.showToast({ 
            title: res.message || '划转失败', 
            icon: 'none',
            duration: 2000,
            mask: true
          });
        }
      }).catch(error => {
        uni.hideLoading();
        console.error('划转失败:', error);
        // 显示具体的错误信息
        let errorMsg = '网络错误，请重试';
        if (error.response && error.response.data && error.response.data.message) {
          errorMsg = error.response.data.message;
        } else if (error.message) {
          errorMsg = error.message;
        }
        uni.showToast({ 
          title: errorMsg,
          icon: 'none',
          duration: 2000,
          mask: true
        });
      });
    },
    handleTeam() {
      uni.navigateTo({
        url: '/pages/team/index'
      })
    },
    handleInvite() {
      uni.navigateTo({
        url: '/pages/share/index'
      })
    },
    goToAsset() {
      uni.navigateTo({
        url: '/pages/asset/index'
      })
    },
    goToProfit() {
      uni.navigateTo({ url: '/pages/profit/index' })
    },
    handleWithdraw() {
      console.log('提现按钮被点击');
      uni.navigateTo({ url: '/pages/withdraw/index' })
    },
    getAccountBalance(idx) {
      const type = this.accountList[idx]?.type;
      if (type === 'fund') return this.availableBalance;
      if (type === 'commission') return this.commissionBalance;
      if (type === 'copy') return this.copyTradeBalance;
      if (type === 'profit') return this.profitBalance;
      return '0.00';
    },
    goToUploadAvatar() {
      uni.navigateTo({ url: '/pages/mine/upload-avatar' })
    },
    goToLeaderCenter() {
      uni.navigateTo({ url: '/pages/leader/index' });
    },
  }
}
</script>

<style lang="scss">
@import "@/styles/theme.scss";
.container {
  min-height: 100vh;
  background: #121212;
  // padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 94rpx;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

 

.section-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  letter-spacing: 1px;
}

.asset-label, .menu-text {
  color: #fff;
  // font-weight: 500;
}

.stat-item .value {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
}

.stat-item .label {
  color: #fff;
}

.divider {
  background: linear-gradient(to bottom, transparent, #FFD70044, transparent);
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  display: block;
  margin: 0 auto;
  box-shadow: none;
  filter: none;
  background: none;
}

.icp-info {
  color: #FFD70099;
}

// SVG图标统一金色
.icon-svg, svg {
  stroke: #fff !important;
  fill: #fff !important;
}

.glass-effect {
  background: $glass-bg;
  border: 1px solid $glass-border;
  backdrop-filter: blur(20px);
  box-shadow: $glass-shadow;
}

.user-info {
  background: #111111;
  border: 1px solid rgba(255,215,0,0.18);
  border-radius: 18rpx;
  box-shadow: 0 0 20rpx rgba(255,215,0,0.10);
  margin: 20rpx 30rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;

  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    margin-right: 40rpx;
    position: relative;
    overflow: hidden;
    border: none;
    box-shadow: none;
  }

  .user-details {
    display: flex;
    flex-direction: column;
    gap: 10rpx;
    
    text {
      color: #fff;
      font-size: 28rpx;
      font-weight: 500;
      margin-bottom: 6rpx;
      &.account {
        font-size: 26rpx;
        font-weight: bold;
        color: #fff;
      }
      &.uid, &.invite-code {
        color: #fff;
        font-size: 26rpx;
        font-weight: 500;
      }
    }
  }
}

.stats-card {
  background: #111111;
  border: 1px solid rgba(255,215,0,0.18);
  box-shadow: 0 0 20rpx rgba(255,215,0,0.10);
  margin: 20rpx 30rpx;
  border-radius: $border-radius;
  padding: 30rpx;
  position: relative;
  overflow: hidden;
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(34, 209, 238, 0.1), transparent);
    opacity: 0.5;
    z-index: 1;
  }

  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    width: 100%;
  }

  .stats-title {
    font-size: 32rpx;
    color: #fff;
    font-weight: bold;
    letter-spacing: 1px;
  }

  .recharge-btn, .transfer-btn {
    z-index: 10;
    position: relative;
    margin-right: 0;
    min-width: 140rpx;
    border-radius: 16rpx;
    padding: 0 40rpx;
    height: 56rpx;
    line-height: 56rpx;
    font-size: 24rpx;
    font-weight: 500;
    transition: background 0.2s, color 0.2s;
    margin-left: 18rpx;
    border: 2rpx solid #16C784;
  }
  .transfer-btn {
    background: #FFD700;
    color: #000;
  }
  .recharge-btn {
    background: #FFD700;
    color: #000;
  }
  .recharge-btn:active, .transfer-btn:active {
    opacity: 0.85;
  }

  .account-grid {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  .account-row {
    display: flex;
    justify-content: center;
    gap: 32rpx;
    margin-bottom: 18rpx;
  }

  .account-card {
    position: relative;
    z-index: 10;
    cursor: pointer;
    background: #181818;
    border: 1px solid rgba(255,215,0,0.18);
    box-shadow: 0 2rpx 8rpx rgba(255,215,0,0.10);
    border-radius: 18rpx;
    padding: 24rpx 0;
    min-width: 160rpx;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .account-name {
    font-size: 24rpx;
    color: #fff;
    opacity: 0.85;
    font-weight: 500;
    margin-bottom: 8rpx;
  }

  .account-balance {
    font-size: 24rpx;
    color: #fff;
    font-weight: 700;
    letter-spacing: 1px;
  }
}

.stats-summary {
  display: flex;
  margin-top: 18rpx;
  background: #181818;
  border-radius: 14rpx;
  border: 1px solid rgba(255,215,0,0.12);
  box-shadow: 0 2rpx 8rpx rgba(255,215,0,0.08);
  overflow: hidden;
}

.summary-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 18rpx 0;
  border-right: 1px solid rgba(255,215,0,0.08);

  &:last-child {
    border-right: none;
  }

  .summary-label {
    color: #fff;
    font-size: 22rpx;
    margin-bottom: 6rpx;
    font-weight: 500;
  }
  .summary-value {
    color: #fff;
    font-size: 28rpx;
    font-weight: bold;
  }
}

.summary-item.main {
  background: rgba(255,215,0,0.06);
}

.section-card {
  margin: 20rpx 30rpx;
  border-radius: $border-radius;
  padding: 30rpx;
  background: $card-bg;
  border: 1px solid $card-border;
  backdrop-filter: blur(20px);

  .section-title {
    font-size: 32rpx;
    color: $text-primary;
    margin-bottom: 30rpx;
    font-weight: 500;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8rpx;
    
    &::before,
    &::after {
      content: '';
      height: 2px;
      width: 20rpx;
      background: rgba(34, 209, 238, 0.3);
      border-radius: 4rpx;
    }
  }

  .asset-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;
  }

  .asset-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;
    padding: 16rpx 0;
  }

  .grid-menu {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;

    .menu-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12rpx;
      padding: 16rpx 0;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      .menu-icon {
        width: 90rpx;
        height: 90rpx;
        padding: 5rpx;
        transition: all 0.3s ease;
        
        filter: drop-shadow(0 0 8rpx rgba(34, 209, 238, 0.2));
        
        image-rendering: -webkit-optimize-contrast;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        
        // 添加SVG特定样式
        &[viewBox] {
          padding: 15rpx;
          filter: drop-shadow(0 0 8rpx rgba(34, 209, 238, 0.2));
          
          &:active {
            filter: drop-shadow(0 0 12rpx rgba(34, 209, 238, 0.3));
          }
        }
      }

      .menu-text {
        font-size: 24rpx;
        color: $text-secondary;
        transition: color 0.3s ease;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      &:active {
        .menu-icon {
          filter: drop-shadow(0 0 12rpx rgba(34, 209, 238, 0.3));
          transform: scale(1.05);
          background: $active-bg;
        }
        
        .menu-text {
          color: $text-primary;
        }
      }
    }
  }

  &:last-child {
    margin-bottom: 30rpx;
  }
}

.qrcode-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
  
  .popup-content {
    background: #fff;
    border-radius: 20rpx;
    width: 500rpx;
    padding: 25rpx 0rpx;
    position: relative;
    animation: scaleIn 0.3s ease;
    
    .close-btn {
      position: absolute;
      right: 10rpx;
      top: 10rpx;
      width: 60rpx;
      height: 60rpx;
      line-height: 60rpx;
      text-align: center;
      font-size: 40rpx;
      color: #999;
      
      &:active {
        opacity: 0.7;
      }
    }
    
    .qrcode-image {
      width: 400rpx;
      height: 400rpx;
      margin: 0 auto;
      display: block;
    }
    
    .popup-text {
      font-size: 28rpx;
      color: #333;
      text-align: center;
      display: block;
      margin-top: 10rpx;
      padding: 0 15rpx;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.asset-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 90rpx;
}
.asset-icon .icon-svg {
  width: 60rpx !important;
  height: 60rpx !important;
  min-width: 60rpx !important;
  min-height: 60rpx !important;
  max-width: 60rpx !important;
  max-height: 60rpx !important;
  display: block;
  margin: 0 auto;
}

.asset-label {
  font-size: 24rpx;
  color: #fff;
  font-weight: 400;
  line-height: 1.2;
  margin-top: 10rpx;
  text-align: center;
}

.asset-extra-card {
  display: flex;
  gap: 24rpx;
  margin: 24rpx 0 0 0;
  padding: 0 30rpx;
  .extra-item {
    flex: 1;
    background: #181818;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    padding: 18rpx 0 18rpx 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(255,215,0,0.08);
    border: 1px solid rgba(255,215,0,0.12);
    min-width: 0;
    .extra-icon {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 18rpx;
      image {
        width: 60rpx;
        height: 60rpx;
      }
    }
    .extra-label {
      color: #fff;
      font-size: 24rpx;
      font-weight: 600;
    }
    &.profit {
      justify-content: flex-start;
      .extra-info {
        display: flex;
        flex-direction: column;
        gap: 4rpx;
      }
      .extra-value {
        color: #FFE066;
        font-size: 24rpx;
        font-weight: bold;
      }
    }
  }
}

.leader-center-card {
  margin: 24rpx 30rpx 0 30rpx;
  // background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
   background: linear-gradient(90deg, #0D1014 0%, #0D1014 100%);
   border: 1px solid rgba(255,215,0,0.12);
  border-radius: 18rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(30, 58, 138, 0.2);

  .leader-content {
    display: flex;
    align-items: center;

    .leader-icon-wrapper {
      width: 80rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      border: 2rpx solid rgba(255, 255, 255, 0.2);

      .leader-icon {
        width: 50rpx;
        height: 50rpx;
        filter: brightness(0) invert(1);
      }
    }

    .leader-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 6rpx;

      .leader-title {
        color: #1AEEBE;
        font-size: 28rpx !important;
        font-weight: 500;
      }

      .leader-desc {
        color: rgba(255, 255, 255, 0.7);
        font-size: 24rpx;
      }
    }

    .leader-gold-img {
      width: 70rpx;
      height: 70rpx;
      margin-left: 12rpx;
      margin-right: 18rpx;
      opacity: 0.9;
    }
  }

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.section-divider {
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 215, 0, 0.3) 50%, transparent 100%);
  margin: 32rpx 60rpx 16rpx 60rpx;
}

.invite-banner {
  margin: 24rpx 30rpx 0 30rpx;
  padding: 24rpx;
  background: linear-gradient(90deg, #0D1014 0%, #0D1014 100%);
  
 border: 1px solid rgba(255,215,0,0.12);
  border-radius: 18rpx;

  box-shadow: 0 4rpx 16rpx rgba(30, 58, 138, 0.2);
  
  
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 120rpx;
  position: relative;
  overflow: hidden;
  // box-shadow: 0 2rpx 12rpx rgba(200,200,0,0.08);

  .invite-content {
    display: flex;
    align-items: center;
    flex: 1;
    z-index: 1;

    .invite-icon-wrapper {
      width: 80rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      border: 2rpx solid rgba(255, 255, 255, 0.3);

      .invite-icon {
        width: 50rpx;
        height: 50rpx;
        filter: brightness(0) invert(1);
      }
    }

    .invite-text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
    }
  }

  .invite-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #1AEEBE;
    margin-bottom: 10rpx;
  }
  .invite-desc {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.7);
  }
  .invite-img {
    width: 70rpx;
    height: 70rpx;
    margin-right: 18rpx;
    z-index: 1;
    opacity: 0.9;
  }
}

.cat-balance-row {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 12rpx;
  .cat-label {
    color: #fff;
    font-size: 26rpx;
    font-weight: bold;
    margin-right: 12rpx;
  }
  .cat-balance {
    color: #FFE066;
    font-size: 26rpx;
    font-weight: bold;
  }
}
.transfer-popup, .password-popup {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.6);
  z-index: 900;  // 降低遮罩层的z-index
  display: flex;
  align-items: center;
  justify-content: center;
}
.transfer-popup .popup-content, .password-popup .popup-content {
  background: #222;
  border-radius: 18rpx;
  width: 540rpx;
  padding: 40rpx 30rpx 30rpx 30rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  z-index: 901;  // 确保弹窗内容在遮罩层之上
}
.popup-title {
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
  text-align: center;
}
.picker {
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
  background: #181818;
  border-radius: 12rpx;
  padding: 16rpx 18rpx;
}
.picker-label {
  color: #fff;
  font-size: 26rpx;
  width: 120rpx;
}
.picker-value {
  color: #fff;
  font-size: 26rpx;
}
.input-row {
  margin-bottom: 18rpx;
}
.amount-input {
  background: #181818 !important;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.18);
  border: none;
  width: 100%;
  height: 80rpx;
  padding: 0 18rpx;
  font-size: 32rpx;
  border-radius: 12rpx;
  color: #fff;
  margin-bottom: 18rpx;
  outline: none;
  box-sizing: border-box;
  font-family: inherit;
  display: flex;
  align-items: center;
  transition: background 0.2s;
}
.amount-input::placeholder {
  color: #888;
  font-size: 28rpx;
  font-family: inherit;
}
.amount-input:focus {
  background: #181818;
}
.confirm-btn {
  background: #fff;
  color: #222;
  font-size: 30rpx;
  border-radius: 32rpx;
  font-weight: bold;
  width: 220rpx;
  height: 56rpx;
  line-height: 56rpx;
  display: block;
  margin: 20rpx auto 0 auto;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(255,215,0,0.10);
  border: none;
  transition: background 0.2s, color 0.2s;
}
.confirm-btn:active {
  background: #ffe066;
  color: #111;
}
.close-btn {
  position: absolute;
  right: 18rpx;
  top: 12rpx;
  font-size: 40rpx;
  color: #fff;
  z-index: 10;
  font-weight: bold;
  cursor: pointer;
}
.custom-select {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
  background: #181818;
  border-radius: 12rpx;
  padding: 16rpx 18rpx;
  cursor: pointer;
  .picker-label {
    color: #fff;
    font-size: 26rpx;
    width: 120rpx;
  }
  .picker-value {
    color: #fff;
    font-size: 26rpx;
    flex: 1;
  }
  .arrow {
    color: #fff;
    font-size: 22rpx;
    margin-left: 10rpx;
  }
  .dropdown-list {
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    background: #222;
    border-radius: 0 0 12rpx 12rpx;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.18);
    z-index: 10;
    max-height: 300rpx;
    overflow-y: auto;
  }
  .dropdown-item {
    padding: 18rpx 24rpx;
      color: #fff;
    font-size: 26rpx;
    cursor: pointer;
    &:hover {
      background: #333;
    }
  }
}
.balance-row {
  color: #fff; 
  font-size: 26rpx;
  margin: 10rpx 0 10rpx 0;
  text-align: right;
}
.password-input {
  background: #111 !important;
  border: 1px solid rgba(255,215,0,0.12);
  width: 100%;
  height: 80rpx;
  padding: 0 18rpx;
  font-size: 32rpx;
  border-radius: 12rpx;
  color: #fff;  // 修改字体颜色为白色
  margin-bottom: 18rpx;
  outline: none;
  box-sizing: border-box;
  font-family: inherit;
  display: flex;
  align-items: center;
  transition: background 0.2s;
}
.password-input::placeholder {
  color: rgba(255,255,255,0.5);  // 修改占位符颜色为半透明白色
  font-size: 28rpx;
  font-family: inherit;
}

// 添加全局样式确保uni-toast显示在最上层
::v-deep .uni-toast {
  z-index: 999999 !important;
}
.stats-btn-row {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
  margin-bottom: 18rpx;
  z-index: 1000;
  position: relative;
}
.withdraw-btn {
  z-index: 1001;
  position: relative;
  font-size: 24rpx !important;
}
.stats-btn-row button {
  flex: 1;
  min-width: 0;
  margin: 0;
  height: 56rpx;
  font-size: 26rpx;
  font-weight: 500;
  border-radius: 16rpx;
  transition: background 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
.recharge-btn {
  background: #fff !important;
  color: #111 !important;
  border: none !important;
}
.withdraw-btn, .transfer-btn {
  background: #18191D !important;
  color: #fff !important;
  border: 2rpx solid #fff !important;
}
</style> 