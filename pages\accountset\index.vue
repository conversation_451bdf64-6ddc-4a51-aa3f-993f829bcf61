<template>
  <view class="accountset-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">账户设置</text>
        <view class="right-area"></view>
      </view>
    </view>
    <!-- 设置列表 -->
    <view class="content-box">
      <view class="settings-list">
        <!-- 设置项 -->
        <view class="setting-item" v-for="(item, index) in settingsList" :key="index" @click="handleSettingClick(item)">
          <text class="item-text">{{ item.title }}</text>
          <uni-icons type="right" size="16" color="#fff"></uni-icons>
        </view>
      </view>
      <!-- 退出登录按钮 -->
      <button class="logout-btn" @click="handleLogout">退出登录</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 0,
      settingsList: [
        { title: '修改登陆密码', path: '/pages/updatepassword/index' },
        { title: '设置安全密码', path: '/pages/updatesafepwd/index' },
      ]
    }
  },
  created() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    handleSettingClick(item) {
      if (item.path) {
        uni.navigateTo({
          url: item.path
        })
      }
    },
    handleLogout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            uni.clearStorageSync();
            uni.showToast({
              title: '已退出登录',
              icon: 'success',
              duration: 2000
            })
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/login/index'
              })
            }, 2000)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss">
.accountset-container {
  min-height: 100vh;
  background: #121212 !important;
  .custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #121212 !important;
    .navbar-content {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;
      .left-area {
        width: 80rpx;
        height: 44px;
        display: flex;
        align-items: center;
        &:active {
          opacity: 0.7;
        }
      }
      .page-title {
        color: #fff;
        font-size: 30rpx;
        font-weight: 500;
      }
      .right-area {
        width: 80rpx;
      }
    }
  }
  .content-box {
    padding: calc(var(--status-bar-height) + 44px + 20rpx) 30rpx 30rpx;
    .settings-list {
      background: #18191D !important;
      border-radius: 16rpx;
      box-shadow: 0 2rpx 8rpx #FFD70022;
      overflow: hidden;
      .setting-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
        border-bottom: 1px solid #FFD70022;
        transition: all 0.3s ease;
        &:last-child {
          border-bottom: none;
        }
        &:active {
          background: #FFD70022;
          transform: scale(0.98);
        }
        .item-text {
          color: #fff;
          font-size: 28rpx;
        }
      }
    }
    .logout-btn {
      width: 100%;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      font-size: 28rpx;
      margin-top: 60rpx;
      background: #fff !important;
      color: #18191D !important;
      border: none;
      border-radius: 12rpx;
      font-weight: 500;
      box-shadow: 0 2rpx 8rpx #FFD70044;
      transition: background 0.2s, color 0.2s;
      &:active {
        opacity: 0.8;
        transform: scale(0.98);
        background: #eee !important;
        color: #18191D !important;
      }
    }
  }
}
</style>
