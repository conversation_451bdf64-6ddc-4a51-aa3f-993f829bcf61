<template>
  <view class="team-container" :style="{ paddingTop: navPaddingTop + 'px' }">
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">我的团队</text>
        <view class="right-area"></view>
      </view>
    </view>
    <!-- 团队业绩统计 -->
    <view class="team-stats">
      <view class="stats-row">
        <view class="stats-col">
          <text class="stats-label">今日推荐</text>
          <text class="stats-value">{{ teamStats.todayRecommend }}</text>
        </view>
        <view class="stats-col">
          <text class="stats-label">直推总数</text>
          <text class="stats-value">{{ teamStats.directTotal }}</text>
        </view>
        <view class="stats-col">
          <text class="stats-label">有效直推</text>
          <text class="stats-value">{{ teamStats.validDirect }}</text>
        </view>
        <view class="stats-col">
          <text class="stats-label">我的团队</text>
          <text class="stats-value">{{ teamStats.teamTotal }}</text>
        </view>
      </view>
    </view>
    <!-- tab切换 -->
    <view class="tab-bar">
      <view class="tab" :class="{ active: activeTab === 0 }" @click="onTabChange(0)">直推记录</view>
      <view class="tab" :class="{ active: activeTab === 1 }" @click="onTabChange(1)">有效记录</view>
      <view class="tab" :class="{ active: activeTab === 2 }" @click="onTabChange(2)">今日记录</view>
    </view>
    <!-- 记录列表卡片 -->
    <view class="record-card">
      <view v-for="item in recordList" :key="item.userNo" class="record-item">
        <text class="record-uid">{{ item.userNo }}</text>
        <text class="record-username">{{ item.username }}</text>
        <text
          v-if="activeTab === 1"
          class="record-commission"
          :class="{ clickable: canAssignCommission(item) }"
          @click="onAssignCommission(item)"
        >
          佣金比例: {{ item.commissionRate }}%
        </text>
        <text
          v-else
          class="record-time"
        >
          {{ formatTime(item.createTime) }}
        </text>
      </view>
      <view v-if="loading" class="loading-text">加载中...</view>
      <view v-if="!loading && recordList.length === 0" class="empty-text">暂无数据</view>
      <view v-if="!loading && recordList.length < total" class="load-more" @click="loadMore">加载更多</view>
    </view>
    <!-- 分配佣金弹窗 -->
    <view v-if="showAssignDialog" class="assign-dialog-mask">
      <view class="assign-dialog">
        <view class="assign-title">分配佣金比例</view>
        <view class="assign-info-row">
          <text class="assign-label">我的佣金比例：</text>
          <text class="assign-my-rate">{{ myCommissionRate }}%</text>
        </view>
        <view class="assign-info-row">
          <text class="assign-label">分配给：</text>
          <text class="assign-userid" :title="selectedUser.userNo">{{ selectedUser.userNo }}</text>
        </view>
        <view class="assign-info-row">
          <text class="assign-label">昵称：</text>
          <text class="assign-username" :title="selectedUser.username">
            {{ selectedUser.username && selectedUser.username.length > 6 ? selectedUser.username.slice(0,6) + '...' : selectedUser.username }}
          </text>
        </view>
        <input class="assign-input" type="number" v-model="assignRate" placeholder="请输入分配比例" min="0" :max="myCommissionRate" @input="assignInputChange" />
        <view class="assign-btns">
          <button class="assign-btn" @click="confirmAssign">分配</button>
          <button class="assign-btn cancel" @click="showAssignDialog=false">取消</button>
        </view>
      </view>
    </view>
    <!-- 空状态（保留） -->
    <!-- <view class="empty-state" v-if="false">
      <svg width="180" height="180" viewBox="0 0 180 180" class="empty-img">
        <rect x="40" y="80" width="100" height="60" rx="8" fill="#d6ff3c" />
        <rect x="60" y="60" width="60" height="40" rx="8" fill="#d6ff3c" opacity="0.7"/>
      </svg>
      <text class="empty-text">空空如也</text>
    </view> -->
    <view v-if="showCustomConfirm" class="custom-confirm-mask">
      <view class="custom-confirm-dialog">
        <view class="custom-confirm-title">确认分配</view>
        <view class="custom-confirm-content">{{ confirmContent }}</view>
        <view class="custom-confirm-btns">
          <button class="custom-btn cancel" @click="handleConfirm(false)">取消</button>
          <button class="custom-btn confirm" @click="handleConfirm(true)">确认分配</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
export default {
  data() {
    return {
      statusBarHeight: 0,
      navBarHeight: 44,
      navPaddingTop: 44,
      activeTab: 0,
      teamStats: {
        todayRecommend: 0,
        directTotal: 0,
        validDirect: 0,
        teamTotal: 0
      },
      recordList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      showAssignDialog: false,
      selectedUser: {},
      assignRate: '',
      myCommissionRate: 0,
      showCustomConfirm: false,
      confirmContent: '',
      confirmCallback: null,
    }
  },
  async created() {
    const systemInfo = uni.getSystemInfoSync()
    await this.getMyCommissionRate()
    await this.getTeamStats()
    await this.getRecordList(true)
  },
  methods: {
    async getTeamStats() {
      try {
        const res = await request({
          url: '/api/user/team-stats',
          method: 'GET'
        })
        const data = res.data || res
        if ((res.code === 200 || res.success) && data) {
          this.teamStats = data
        }
      } catch (e) {
        uni.showToast({ title: '获取团队数据失败', icon: 'none' })
      }
    },
    async getMyCommissionRate() {
      try {
        const res = await request({ url: '/api/user/info', method: 'GET' })
        if (res.code === 200 && res.data) {
          this.myCommissionRate = res.data.commissionRate || 0
        }
      } catch {}
    },
    async getRecordList(reset = false) {
      this.loading = true
      if (reset) this.page = 1
      const typeArr = ['direct', 'valid', 'today']
      const type = typeArr[this.activeTab]
      try {
        const res = await request({
          url: '/api/user/team-records',
          method: 'GET',
          data: {
            type,
            page: this.page,
            size: this.pageSize
          }
        })
        if (res.code === 200 && res.data) {
          this.recordList = reset ? res.data.records : this.recordList.concat(res.data.records)
          this.total = res.data.total
        }
      } finally {
        this.loading = false
      }
    },
    onTabChange(idx) {
      this.activeTab = idx
      this.getRecordList(true)
    },
    loadMore() {
      if (this.recordList.length < this.total && !this.loading) {
        this.page++
        this.getRecordList()
      }
    },
    formatTime(val) {
      if (!val) return ''
      const d = new Date(val)
      return `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}-${d.getDate().toString().padStart(2,'0')} ${d.getHours().toString().padStart(2,'0')}:${d.getMinutes().toString().padStart(2,'0')}`
    },
    handleBack() {
      uni.navigateBack()
    },
    canAssignCommission(item) {
      // 只有佣金为0且自己佣金比例大于0才可分配
      return this.activeTab === 1 && item.commissionRate == 0 && this.myCommissionRate > 0
    },
    async onAssignCommission(item) {
      // 再查一次自己佣金比例，保证分配时是最新的
      await this.getMyCommissionRate()
      if (!this.canAssignCommission(item)) return
      this.selectedUser = item
      this.assignRate = ''
      this.showAssignDialog = true
    },
    showConfirm(content, callback) {
      this.confirmContent = content
      this.showCustomConfirm = true
      this.confirmCallback = callback
    },
    handleConfirm(ok) {
      this.showCustomConfirm = false
      if (ok && typeof this.confirmCallback === 'function') {
        this.confirmCallback()
      }
    },
    async confirmAssign() {
      // 前端校验
      const val = Number(this.assignRate)
      if (!val || val <= 0) {
        uni.showToast({ title: '请输入大于0的分配比例', icon: 'none' })
        return
      }
      if (val > this.myCommissionRate) {
        uni.showToast({ title: '分配比例不能大于自己可分配佣金', icon: 'none' })
        return
      }
      // 使用自定义弹窗
      this.showConfirm('一旦分配，不可再进行修改，是否确认分配？', async () => {
        try {
          const res = await request({
            url: '/api/user/assign-commission',
            method: 'POST',
            data: {
              userNo: this.selectedUser.userNo,
              assignRate: val
            }
          })
          if (res.code === 200) {
            uni.showToast({ title: '分配成功', icon: 'success' })
            this.showAssignDialog = false
            this.getRecordList(true)
          } else {
            uni.showToast({ title: res.msg || '分配失败', icon: 'none' })
          }
        } catch (e) {
          uni.showToast({ title: '分配失败', icon: 'none' })
        }
      })
    },
    assignInputChange(e) {
      const val = Number(e.detail.value)
      if (val > this.myCommissionRate) {
        uni.showToast({ title: '分配比例不能大于自己可分配佣金', icon: 'none' })
        this.assignRate = this.myCommissionRate
      }
    }
  }
}
</script>

<style lang="scss" scoped>
html, body {
  height: 100%;
  overflow: hidden !important;
}
.team-container {
  min-height: 100vh;
  background: #121212;
  overflow: hidden;
}
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #121212 !important;
  box-shadow: none !important;
  /* border-bottom: none !important; */
}
.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.left-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
}
.right-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.page-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 30rpx !important;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.team-stats {
  background: #181818;
  border-radius: 18rpx;
  margin: 32rpx 24rpx 0 24rpx;
  padding: 32rpx 24rpx 12rpx 24rpx;
  box-shadow: 0 2rpx 8rpx #FFD70022;
  .stats-row {
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    .stats-col {
      display: flex;
      flex-direction: column;
      align-items: center;
      .stats-label {
        color: #fff;
        font-size: 26rpx;
        margin-bottom: 8rpx;
      }
      .stats-value {
        color: #fff;
        font-size: 24rpx;
        font-weight: bold;
      }
      &.main {
        .stats-label, .stats-value {
          color: #fff;
        }
        .stats-value {
          font-size: 38rpx;
        }
      }
    }
  }
}
.tab-bar {
  display: flex;
  background: #121212;
  border-radius: 12rpx;
  margin: 24rpx 24rpx 0 24rpx;
  padding: 0 8rpx;
  .tab {
    flex: 1;
    text-align: center;
    padding: 16rpx 0;
    font-size: 26rpx;
    color: #fff;
    background: #121212;
    border-bottom: 4rpx solid transparent;
    transition: color 0.2s, border-color 0.2s, background 0.2s;
    &:first-child {
      border-top-left-radius: 12rpx;
      border-bottom-left-radius: 12rpx;
    }
    &:last-child {
      border-top-right-radius: 12rpx;
      border-bottom-right-radius: 12rpx;
    }
    &.active {
      color: #111;
      background: #fff;
      border-bottom: 4rpx solid #fff;
      font-weight: bold;
    }
  }
}
.record-card {
  background: #18191D;
  border-radius: 16rpx;
  margin: 24rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx #FFD70022;
  max-height: 60vh;
  overflow-y: auto;
  .record-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 24rpx 0;
    border-bottom: 1px solid #232323;
    font-size: 24rpx;
    color: #fff;
    .record-uid {
      min-width: 120rpx;
      max-width: 180rpx;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 24rpx;
      padding: 0 8rpx;
      font-size: 20rpx;
    }
    .record-username {
      flex: 1;
      min-width: 0;
      text-align: center;
      font-weight: bold;
      padding: 0 16rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 20rpx;
    }
    .record-commission,
    .record-time {
      min-width: 180rpx;
      max-width: 220rpx;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-left: 24rpx;
      padding: 0 8rpx;
      font-size: 20rpx;
    }
  }
}
.loading-text, .empty-text, .load-more {
  text-align: center;
  color: #fff;
  font-size: 26rpx;
  padding: 24rpx 0;
}
.load-more {
  cursor: pointer;
}
.record-commission.clickable {
  cursor: pointer;
  color: #fff;
  text-decoration: underline;
}
.assign-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.45);
  z-index: 998;
  display: flex;
  align-items: center;
  justify-content: center;
}
.assign-dialog {
  background: #232323;
  border-radius: 18rpx;
  padding: 36rpx 24rpx 24rpx 24rpx;
  min-width: 540rpx;
  max-width: 98vw;
  border: 1.5px solid #FFD700;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  z-index: 1001;
}
.assign-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  text-align: center;
  letter-spacing: 2rpx;
}
.assign-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 14rpx;
  font-size: 26rpx;
  color: #FFD70099;
  .assign-label {
    min-width: 100rpx;
    color: #FFD70099;
  }
  .assign-my-rate {
    color: #FFD700;
    font-weight: bold;
    font-size: 28rpx;
    margin-left: 8rpx;
  }
  .assign-userid, .assign-username {
    color: #FFD700;
    font-weight: bold;
    max-width: 180rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 8rpx;
  }
}
.assign-input {
  margin: 18rpx 0 24rpx 0;
  padding: 18rpx 16rpx;
  border-radius: 12rpx;
  border: none !important;
  font-size: 30rpx;
  color: #FFD700;
  background: #181818 !important;
  outline: none;
  box-shadow: none !important;
  caret-color: #FFD700;
}
/* 兼容uni-app/微信小程序 placeholder 颜色 */
.assign-input::-webkit-input-placeholder { color: #888; opacity: 1; }
.assign-input:-moz-placeholder { color: #888; opacity: 1; }
.assign-input::-moz-placeholder { color: #888; opacity: 1; }
.assign-input:-ms-input-placeholder { color: #888; opacity: 1; }

.assign-btns {
  display: flex;
  justify-content: space-between;
  gap: 18rpx;
  margin-top: 4rpx;
}
.assign-btn {
  flex: 1;
  background: #d6ff3c;
  color: #222;
  border: none;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: bold;
  padding: 7rpx 0;
  margin: 0;
  transition: background 0.2s;
}
.assign-btn:active {
  background: #c0e800;
}
.assign-btn.cancel {
  background: #333;
  color: #FFD70099;
}
.custom-confirm-mask {
  position: fixed; left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.45);
  z-index: 2000;
  display: flex; align-items: center; justify-content: center;
}
.custom-confirm-dialog {
  background: #232323;
  border-radius: 18rpx;
  min-width: 480rpx;
  max-width: 90vw;
  border: 1.5px solid #FFD700;
  box-shadow: 0 2rpx 12rpx #FFD70033;
  display: flex; flex-direction: column; align-items: stretch;
  padding: 36rpx 24rpx 24rpx 24rpx;
}
.custom-confirm-title {
  color: #FFD700;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 18rpx;
}
.custom-confirm-content {
  color: #FFD70099;
  font-size: 26rpx;
  text-align: center;
  margin-bottom: 28rpx;
}
.custom-confirm-btns {
  display: flex; justify-content: space-between; gap: 18rpx;
}
.custom-btn {
  flex: 1;
  border-radius: 8rpx;
  font-size: 22rpx;
  font-weight: bold;
  padding: 7rpx 0;
  margin: 0;
  border: none;
}
.custom-btn.cancel {
  background: #333;
  color: #FFD70099;
}
.custom-btn.confirm {
  background: #d6ff3c;
  color: #222;
}
</style> 