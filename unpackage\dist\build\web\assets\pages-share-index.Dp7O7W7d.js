import{g as e,K as s,s as a,e as t,f as o,h as i,w as r,i as c,o as n,j as l,m as d,t as u,x as h,v as C,y as p}from"./index-BnjgV7rC.js";import{C as f}from"./custom-navbar.Bf8ZgRC_.js";import{r as v}from"./uni-app.es.BuhgQoIe.js";import{u as m}from"./uqrcode.BN_KsmDm.js";import{_}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-icons.9b9P_X39.js";const k=_({components:{CustomNavbar:f},data:()=>({shareCode:"",qrCodeLink:"",qrCodeUrl:""}),onShow(){this.getShareCode()},methods:{async getShareCode(){const s=e("userInfo");s&&(this.shareCode=s.shareCode||"",this.qrCodeLink=`https://invite.catcoinvip.com?invitecode=${this.shareCode}`,this.generateQrCode())},async generateQrCode(){try{const e=await m.generate(this.qrCodeLink);this.qrCodeUrl=e.tempFilePath}catch(e){console.error("生成二维码失败:",e)}},copyText(e){s({data:e,success:()=>{a({title:"已复制",icon:"success"})}})},shareToFriend(){this.copyText(this.qrCodeLink)},goInviteRecord(){t({url:"/pages/share/record"})}}},[["render",function(e,s,a,t,m,_){const k=v(o("custom-navbar"),f),q=h,y=c,g=C,x=p;return n(),i(y,{class:"container"},{default:r((()=>[l(k,{title:"邀请好友",showBack:!0}),l(y,{class:"content"},{default:r((()=>[l(y,{class:"invite-card"},{default:r((()=>[l(y,{class:"invite-header"},{default:r((()=>[l(q,{class:"invite-title"}),l(q,{class:"invite-desc"},{default:r((()=>[d("探索区块链交易新机遇")])),_:1})])),_:1}),l(y,{class:"qrcode-box"},{default:r((()=>[m.qrCodeUrl?(n(),i(g,{key:0,src:m.qrCodeUrl,mode:"aspectFit",class:"qrcode-img"},null,8,["src"])):(n(),i(q,{key:1,class:"qrcode-loading"},{default:r((()=>[d("二维码生成中...")])),_:1}))])),_:1}),l(y,{class:"invite-row"},{default:r((()=>[l(q,{class:"invite-label"},{default:r((()=>[d("邀请码")])),_:1}),l(q,{class:"invite-value"},{default:r((()=>[d(u(m.shareCode),1)])),_:1}),l(y,{class:"copy-icon",onClick:s[0]||(s[0]=e=>_.copyText(m.shareCode))})])),_:1}),l(y,{class:"invite-row"},{default:r((()=>[l(q,{class:"invite-label"},{default:r((()=>[d("邀请链接")])),_:1}),l(q,{class:"invite-link"},{default:r((()=>[d(u(m.qrCodeLink),1)])),_:1}),l(y,{class:"copy-icon",onClick:s[1]||(s[1]=e=>_.copyText(m.qrCodeLink))})])),_:1}),l(x,{class:"invite-btn",onClick:_.shareToFriend},{default:r((()=>[d("马上邀请好友")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-84bc0c84"]]);export{k as default};
