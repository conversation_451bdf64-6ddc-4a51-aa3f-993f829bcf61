<template>
  <view class="record-container" :style="{ paddingTop: navPaddingTop + 'px' }">
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">互转记录</text>
        <view class="right-area"></view>
      </view>
    </view>
    <view class="tab-bar">
      <view
        class="tab"
        :class="{ active: activeTab === 0 }"
        @click="onTabChange(0)"
      >转出记录</view>
      <view
        class="tab"
        :class="{ active: activeTab === 1 }"
        @click="onTabChange(1)"
      >转入记录</view>
    </view>
    <view class="record-card">
      <view v-for="(item, idx) in recordList" :key="item.id" class="record-item">
        <text class="record-username">{{ item.toUsername }}</text>
        <text class="record-commission">-{{ item.amount }}USDT</text>
        <text class="record-time" style="margin-left: 32rpx;">{{ formatTime(item.createTime) }}</text>
      </view>
      <view v-if="loading" class="loading-text">加载中...</view>
      <view v-if="!loading && recordList.length === 0" class="empty-text">暂无数据</view>
      <view v-if="!loading && recordList.length < total" class="load-more" @click="loadMore">加载更多</view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
export default {
  data() {
    return {
      statusBarHeight: 0,
      navBarHeight: 44,
      navPaddingTop: 44,
      activeTab: 0, // 0=转出，1=转入
      recordList: [],
      loading: false,
      total: 0,
      page: 1,
      pageSize: 10,
    }
  },
  created() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.navPaddingTop = this.statusBarHeight + this.navBarHeight
    this.getRecordList(true)
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    onTabChange(idx) {
      this.activeTab = idx
      this.getRecordList(true)
    },
    async getRecordList(reset = false) {
      this.loading = true
      if (reset) this.page = 1
      // 0=转出(type=2), 1=转入(type=1)
      const typeArr = [2, 1]
      const type = typeArr[this.activeTab]
      try {
        const res = await request({
          url: '/api/transfer/list',
          method: 'GET',
          data: {
            page: this.page,
            pageSize: this.pageSize,
            type
          }
        })
        if (res.code === 200 && res.data) {
          this.recordList = reset ? res.data.records : this.recordList.concat(res.data.records)
          this.total = res.data.total
        }
      } finally {
        this.loading = false
      }
    },
    loadMore() {
      if (this.recordList.length < this.total && !this.loading) {
        this.page++
        this.getRecordList()
      }
    },
    formatTime(val) {
      if (!val) return ''
      const d = new Date(val)
      return `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}-${d.getDate().toString().padStart(2,'0')} ${d.getHours().toString().padStart(2,'0')}:${d.getMinutes().toString().padStart(2,'0')}`
    },
  }
}
</script>

<style lang="scss" scoped>
.record-container {
  min-height: 100vh;
  background: #121212;
  // padding-top 由js动态设置
}
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #121212;
}
.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.left-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
}
.right-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.page-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 30rpx !important;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tab-bar {
  display: flex;
  background: #121212;
  // margin-top: 120rpx; // 删除margin，避免空白
  .tab {
    flex: 1;
    text-align: center;
    padding: 24rpx 0;
    font-size: 28rpx;
    color: #fff;
    background: #121212;
    border-bottom: 4rpx solid transparent;
    transition: color 0.2s, border-color 0.2s;
    &.active {
      color: #fff;
      border-bottom: 4rpx solid #fff;
      // 保持tab高亮加粗
      font-weight: bold;
      background: #121212;
    }
  }
}
.record-card {
  padding: 20rpx;
  background: #18191D;
  border-radius: 12rpx;
  margin: 20rpx;
  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 0;
    border-bottom: 1rpx solid #333333;
    &:last-child {
      border-bottom: none;
    }
    .record-uid {
      font-size: 26rpx;
      color: #fff;
      // font-weight: bold; // 移除加粗
    }
    .record-username {
      font-size: 24rpx;
      color: #fff;
      max-width: 160rpx;
      margin-left: 0;
      margin-right: 24rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      // font-weight: bold; // 移除加粗
    }
    .record-commission {
      font-size: 26rpx;
      color: #fff;
      // font-weight: bold; // 移除加粗
    }
    .record-time {
      font-size: 22rpx;
      color: #fff;
      margin-left: 20rpx;
    }
  }
  .loading-text {
    text-align: center;
    padding: 20rpx 0;
    color: #fff;
  }
  .empty-text {
    text-align: center;
    padding: 20rpx 0;
    color: #fff;
	font-size: 26rpx;
  }
  .load-more {
    text-align: center;
    padding: 20rpx 0;
    color: #fff;
    font-size: 26rpx !important;
    // font-weight: bold;
  }
}
// 输入框样式（如有输入框组件）
input, .uni-input-input {
  background: #1E1E1E !important;
  color: #fff !important;
  border: none !important;
  border-radius: 8rpx;
}
// 按钮样式
button, .submit-btn {
  background: #fff !important;
  color: #111 !important;
  border-radius: 8rpx;
  border: none !important;
  font-weight: bold;
}
</style> 