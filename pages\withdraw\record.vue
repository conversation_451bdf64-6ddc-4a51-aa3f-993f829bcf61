<template>
  <view class="withdraw-record-container" :style="{ paddingTop: navPaddingTop + 'px' }">
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">提现记录</text>
        <view class="right-area"></view>
      </view>
    </view>
    <view v-if="records.length === 0 && !loading" class="empty-state">
      <view class="empty-img">
        <svg width="100" height="100" viewBox="0 0 180 180">
          <rect x="40" y="80" width="100" height="60" rx="8" fill="#d6ff3c" />
          <rect x="60" y="60" width="60" height="40" rx="8" fill="#d6ff3c" opacity="0.7"/>
        </svg>
      </view>
      <text class="empty-text">空空如也</text>
    </view>
    <view v-else class="record-list">
      <view v-for="item in records" :key="item.id" class="record-item">
        <view class="row">
          <text class="amount">{{ item.amount }} USDT</text>
          <text class="status" :class="'status-' + item.status">
            {{ item.status === 0 ? '待审核' : item.status === 1 ? '已通过' : '已拒绝' }}
          </text>
        </view>
        <view class="row info">
          <text>到账：{{ item.realAmount }} USDT</text>
          <text>手续费：{{ item.fee }} USDT</text>
        </view>
        <view class="row info">
          <text>地址：{{ item.address }}</text>
        </view>
        <view class="row info">
          <text>时间：{{ formatDateTime(item.createTime) }}</text>
        </view>
        <view class="row info" v-if="item.remark">
          <text>备注：{{ item.remark }}</text>
        </view>
      </view>
      <view v-if="loading" class="loading-text">加载中...</view>
      <view v-if="finished" class="finished-text">没有更多了</view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
export default {
  data() {
    return {
      statusBarHeight: 0,
      navBarHeight: 44,
      navPaddingTop: 44,
      records: [],
      page: 1,
      size: 10,
      total: 0,
      loading: false,
      finished: false
    }
  },
  onPullDownRefresh() {
    this.getRecords(true)
    uni.stopPullDownRefresh()
  },
  onReachBottom() {
    this.getRecords()
  },
  created() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.navPaddingTop = this.statusBarHeight + this.navBarHeight
    this.getRecords()
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    async getRecords(refresh = false) {
      if (this.loading || (this.finished && !refresh)) return
      this.loading = true
      if (refresh) {
        this.page = 1
        this.records = []
        this.finished = false
      }
      try {
        const res = await request({
          url: '/api/withdraw-record/list',
          method: 'GET',
          data: { page: this.page, size: this.size }
        })
        if (res.code === 200 && res.data) {
          const { records, total } = res.data
          this.total = total
          if (refresh) {
            this.records = records
          } else {
            this.records = this.records.concat(records)
          }
          if (this.records.length >= total) {
            this.finished = true
          } else {
            this.page += 1
          }
        }
      } finally {
        this.loading = false
      }
    },
    formatDateTime(val) {
      if (!val) return ''
      return val.replace('T', ' ').replace(/-/g, '-').slice(0, 19)
    }
  }
}
</script>

<style lang="scss" scoped>
.withdraw-record-container {
  min-height: 100vh;
  background: #121212;
}
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #121212;
}
.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.left-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
}
.right-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.page-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 30rpx !important;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 180rpx;
  .empty-img {
    margin-bottom: 32rpx;
  }
  .empty-text {
    color: #fff;
    font-size: 24rpx;
  }
}
.record-list {
  padding: 32rpx;
}
.record-item {
  background: #18191D;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  color: #fff;
  font-size: 26rpx;
  box-shadow: 0 2rpx 8rpx #FFD70022;
}
.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.amount {
  font-size: 26rpx;
  font-weight: bold;
}
.status {
  font-size: 26rpx;
}
.status-0 { color: #FFD700; }
.status-1 { color: #4caf50; }
.status-2 { color: #f44336; }
.info {
  font-size: 24rpx;
  color: #fff;
}
.loading-text, .finished-text {
  text-align: center;
  color: #fff;
  margin: 24rpx 0;
}
</style> 