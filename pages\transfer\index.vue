<template>
  <view class="transfer-container">
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">互转</text>
        <view class="right-area">
          <text class="record-link" @click="goToRecord">互转记录</text>
        </view>
      </view>
    </view>
    <view class="scroll-content">
      <view class="form-section">
        <view class="form-item">
          <text class="label">账户类型</text>
          <picker :range="accountTypes" :value="accountTypeIndex" @change="onAccountTypeChange">
            <view class="input select">{{ accountTypes[accountTypeIndex] }}</view>
          </picker>
        </view>
        <view class="form-item">
          <text class="label">互转账号</text>
          <input class="input" :placeholder="accountTypeIndex === 1 ? '请输入邮箱号' : '请输入UID'" v-model="targetAccount" />
        </view>
        <view class="form-item amount-row">
          <view class="amount-label-row">
            <text class="label">金额</text>
            <text class="available">可用：{{ availableAmount }}USDT</text>
          </view>
          <input class="input" placeholder="请输入金额" v-model="amount" type="number" />
        </view>
        <view class="form-item">
          <text class="label">支付密码</text>
          <input class="input" placeholder="请输入支付密码" v-model="payPwd" password />
        </view>
        <view class="form-item">
          <text class="label">备注</text>
          <input class="input" placeholder="最多15字" v-model="remark" maxlength="15" />
        </view>
        <view class="result-action-row">
          <view class="result-info">
            <text class="result">到账数量：{{ amount && fee !== null ? (Number(amount) - Number(fee)).toFixed(2) : 0 }}USDT</text>
            <text class="result">网络手续费：{{ fee }}USDT</text>
          </view>
          <button class="submit-btn" @click="handleSubmit">确定</button>
        </view>
      </view>
    </view>

    <!-- 确认对话框 -->
    <view v-if="showConfirmDialog" class="custom-dialog-mask">
      <view class="custom-dialog">
        <view class="dialog-title">请确认转账信息</view>
        <view class="dialog-row">转账类型：{{ accountTypes[accountTypeIndex] }}</view>
        <view class="dialog-row">{{ accountTypeIndex === 1 ? '邮箱' : 'UID' }}：{{ targetAccount }}</view>
        <view class="dialog-row">转账金额：{{ amount }} USDT</view>
        <view class="dialog-row">到账数量：{{ amount && fee !== null ? (Number(amount) - Number(fee)).toFixed(2) : 0 }} USDT</view>
        <view class="dialog-row">手续费：{{ fee }} USDT</view>
        <view class="dialog-row" v-if="remark">备注：{{ remark }}</view>
        <view class="dialog-actions">
          <button class="dialog-btn cancel" @click="showConfirmDialog=false">取消</button>
          <button class="dialog-btn confirm" @click="confirmTransfer">确认转账</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
export default {
  data() {
    return {
      statusBarHeight: 0,
      accountTypes: ['UID', '邮箱'],
      accountTypeIndex: 0,
      targetAccount: '',
      amount: '',
      availableAmount: '0',
      payPwd: '',
      remark: '',
      transferParams: {
        minTransfer: 0,
        maxTransfer: 0,
        transferFee: 0,
        enableTransfer: true
      },
      fee: 0,
      showConfirmDialog: false
    }
  },
  watch: {
    amount(val) {
      this.calcFee()
    }
  },
  created() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.getTransferParams().then(() => {
      this.getUserInfo()
    })
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    goToRecord() {
      uni.navigateTo({ url: '/pages/transfer/record' })
    },
    onAccountTypeChange(e) {
      this.accountTypeIndex = e.detail.value
      this.targetAccount = ''
    },
    handleSubmit() {
      // 先进行基本验证
      if (!this.targetAccount || !this.amount || !this.payPwd) {
        uni.showToast({ title: '请填写完整信息', icon: 'none' })
        return
      }
      if (!this.transferParams.enableTransfer) {
        uni.showToast({ title: '当前不允许转账', icon: 'none' })
        return
      }
      if (Number(this.amount) < Number(this.transferParams.minTransfer)) {
        uni.showToast({ title: `最小转账金额为${this.transferParams.minTransfer}`, icon: 'none' })
        return
      }
      if (Number(this.amount) > Number(this.transferParams.maxTransfer)) {
        uni.showToast({ title: `最大转账金额为${this.transferParams.maxTransfer}`, icon: 'none' })
        return
      }
      if (Number(this.amount) <= Number(this.fee)) {
        uni.showToast({ title: '金额需大于手续费', icon: 'none' })
        return
      }

      // 验证通过，显示确认对话框
      this.showConfirmDialog = true
    },
    confirmTransfer() {
      this.showConfirmDialog = false
      this.submitTransfer()
    },
    async getTransferParams() {
      try {
        const res = await request({
          url: '/api/sys/params/transfer-withdraw',
          method: 'GET'
        })
        if (res.code === 200 && res.data) {
          this.transferParams = res.data
        }
      } catch (e) {
        uni.showToast({ title: '获取转账参数失败', icon: 'none' })
      }
    },
    async getUserInfo() {
      try {
        const res = await request({
          url: '/api/user/info',
          method: 'GET'
        })
        if (res.code === 200 && res.data) {
          this.availableAmount = res.data.availableBalance || '0'
        }
      } catch (e) {
        this.availableAmount = '0'
      }
    },
    calcFee() {
      const amt = Number(this.amount)
      const fee = Number(this.transferParams.transferFee || 0)
      if (!isNaN(amt) && !isNaN(fee)) {
        this.fee = fee
      } else {
        this.fee = 0
      }
    },
    async submitTransfer() {
      let data = {
        amount: this.amount,
        remark: this.remark,
        payPwd: this.payPwd
      }
      if (this.targetAccount.includes('@')) {
        data.toEmail = this.targetAccount
      } else {
        data.toUid = this.targetAccount
      }
      try {
        const res = await request({
          url: '/api/transfer/create',
          method: 'POST',
          data
        })
        if (res.code === 200) {
          uni.showToast({ title: '转账成功', icon: 'success' })
          this.targetAccount = ''
          this.amount = ''
          this.remark = ''
          this.payPwd = ''
          this.getUserInfo()
        } else {
          uni.showToast({ title: res.msg || '转账失败', icon: 'none' })
        }
      } catch (e) {
        uni.showToast({ title: e.message, icon: 'none' })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.transfer-container {
  height: 100vh;
  box-sizing: border-box;
  background: #121212 !important;
  padding-bottom: 40rpx;
}
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #121212 !important;
}
.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.left-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
}
.right-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.page-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 30rpx !important;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.scroll-content {
  padding-top: 120rpx;
}
.form-section {
  background: #18191D !important;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx #FFD70011;
  margin-bottom: 24rpx;
  padding: 32rpx 24rpx 0 24rpx;
  margin-left: 24rpx;
  margin-right: 24rpx;
}
.form-item {
  margin-bottom: 32rpx;
  display: flex;
  flex-direction: column;
  .label {
    color: #fff;
    font-size: 28rpx;
    margin-bottom: 12rpx;
  }
  .input,
  .select,
  input {
    background: #1E1E1E !important;
    border-radius: 8rpx;
    height: 56rpx;
	line-height: 56rpx;
    padding: 0 20rpx;
    font-size: 24rpx;
    color: #fff;
    border: none;
    outline: none;
    box-sizing: border-box;
  }
  .input::placeholder,
  input::placeholder {
    color: #fff;
    opacity: 1;
  }
  .available {
    color: #fff;
    font-size: 28rpx;
    margin-top: 8rpx;
    align-self: flex-end;
  }
  &.amount-row {
    flex-direction: column !important;
    align-items: stretch !important;
    .amount-label-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8rpx;
      .label {
        margin-bottom: 0;
        font-size: 28rpx;
        color: #fff;
      }
      .available {
        color: #fff;
        font-size: 28rpx;
        margin-top: 0;
        align-self: flex-end;
      }
    }
    .input {
      width: 100%;
      margin-right: 0;
    }
  }
}
.transfer-form {
  padding: 0 32rpx;
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  input {
    background: #181818;
    border-radius: 8rpx;
    height: 72rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    color: #fff;
    border: 1rpx solid #fff !important;
    outline: none;
  }

  button {
    width: 100%;
    height: 96rpx;
    background: #fff;
    color: #111;
    font-size: 28rpx;
    font-weight: bold;
    border-radius: 8rpx;
    border: none;
    box-shadow: 0 2rpx 8rpx #FFD70044;
    transition: background 0.2s, color 0.2s;
  }

  button:active {
    background: #fff;
    color: #111;
  }
}
.result-action-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  margin-top: 32rpx;
}
.result-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  .result {
    color: #fff;
    font-size: 28rpx;
    margin-bottom: 8rpx;
    display: block;
    text-align: left;
  }
}
.submit-btn {
  width: 200rpx;
  margin: 0 0 0 32rpx;
  height: 56rpx;
  background: #fff !important;
  color: #18191D !important;
  font-size: 24rpx;
  font-weight: bold;
  border-radius: 8rpx;
  border: none;
  box-shadow: 0 2rpx 8rpx #FFD70044;
  transition: background 0.2s, color 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.submit-btn:active {
  background: #eee !important;
  color: #18191D !important;
}
.record-link {
  color: #fff !important;
  font-size: 28rpx !important;
  font-weight: 500;
  margin-right: 0;
  z-index: 10;
  display: inline-block;
  white-space: nowrap;
  padding-left: 12rpx;
  padding-right: 12rpx;
}

/* 确认对话框样式 */
.custom-dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-dialog {
  background: #18191D;
  border-radius: 16rpx;
  min-width: 80%;
  max-width: 90vw;

  box-shadow: 0 4rpx 16rpx rgba(255, 215, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: 32rpx 24rpx 24rpx 24rpx;
}

.dialog-title {
  color: #FFD700;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 24rpx;
}

.dialog-row {
  color: #fff;
  font-size: 28rpx;
  margin-bottom: 16rpx;
  padding: 8rpx 0;
  border-bottom: 1rpx solid #333;
}

.dialog-row:last-of-type {
  border-bottom: none;
  margin-bottom: 24rpx;
}

.dialog-actions {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
  margin-top: 8rpx;
}

.dialog-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-btn.cancel {
  background: #333;
  color: #fff;
}

.dialog-btn.cancel:active {
  background: #444;
}

.dialog-btn.confirm {
  background: #FFD700;
  color: #18191D;
}

.dialog-btn.confirm:active {
  background: #E6C200;
}
</style> 