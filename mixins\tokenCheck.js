import { mapMutations } from 'vuex'

export default {
  onShow() {
    this.checkTokenStatus()
  },
  
  onLoad() {
    this.checkTokenStatus()
  },
  
  methods: {
    async checkTokenStatus() {
      const token = uni.getStorageSync('token')
      if (!token) {
        this.handleNoToken()
        return false
      }
      
      try {
        const tokenData = JSON.parse(atob(token.split('.')[1]))
        const expTime = tokenData.exp * 1000
        
        if (expTime < Date.now()) {
          this.handleTokenExpired()
          return false
        }
        
        return true // token 有效
      } catch (e) {
        console.error('Token解析失败:', e)
        this.handleInvalidToken()
        return false
      }
    },
    
    handleNoToken() {
      // 清除所有登录信息
      this.clearLoginInfo()
      // 跳转到登录页
      this.redirectToLogin()
    },
    
    handleTokenExpired() {
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      })
      this.clearLoginInfo()
      this.redirectToLogin()
    },
    
    handleInvalidToken() {
      uni.showToast({
        title: '登录状态异常，请重新登录',
        icon: 'none'
      })
      this.clearLoginInfo()
      this.redirectToLogin()
    },
    
    clearLoginInfo() {
      // 清除本地存储的登录信息
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
    },
    
    redirectToLogin() {
      // 获取当前页面路径
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const currentPath = `/${currentPage.route}`
      
      // 如果当前不在登录页，则跳转到登录页
      if (currentPath !== '/pages/login/index') {
        uni.redirectTo({
          url: '/pages/login/index'
        })
      }
    }
  }
} 