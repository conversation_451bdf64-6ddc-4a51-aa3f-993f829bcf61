<template>
  <view class="container" >
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">提现</text>
        <view class="right-area">
          <text class="record-link" @click="goToRecord">提现记录</text>
        </view>
      </view>
    </view>
    <view class="scroll-content" :style="{ paddingTop: navPaddingTop + 'px' }">
      <view class="card">
        <view class="form-section">
          <view class="form-item">
            <text class="label">提现地址</text>
            <input class="input" :placeholder="'请输入提现地址'" v-model="address" />
          </view>
          <view class="form-item">
            <text class="label">账户类型</text>
            <picker :range="accountTypes" :value="accountTypeIndex" @change="onAccountTypeChange">
              <view class="input select">{{ accountTypes[accountTypeIndex] }}</view>
            </picker>
          </view>
          <view class="form-item">
            <view class="row-between">
              <text class="label">提现数量</text>
              <view class="available-text">可用：{{ availableBalance }} USDT</view>
            </view>
            <input class="input" :placeholder="withdrawAmountPlaceholder" v-model="amount" type="number" />
          </view>
        
          <view class="form-item">
            <text class="label">支付密码</text>
            <input class="input" :placeholder="'请输入支付密码'" v-model="payPwd" password />
          </view>
          <view class="form-item">
            <text class="label">备注</text>
            <input class="input" :placeholder="'选填，最多20字'" v-model="remark" maxlength="20" />
          </view>
        </view>
        <view class="desc-section">
          <text class="desc-title">提现说明：</text>
          <text class="desc-text">1、由于区块网络匿名和私密，一旦输入错误资讯，您将损失对应资产。请务必仔细核对您的充值及提币地址与数量\n2、{{ withdrawDesc }}</text>
        </view>
        <view class="result-action-row">
          <view class="result-info">
            <text class="result">到账数量：{{ amount && fee !== null ? (Number(amount) - Number(fee)).toFixed(2) : 0 }}USDT</text>
            <text class="result">网络手续费：{{ fee }}USDT</text>
          </view>
          <button class="submit-btn" @click="submit">提现</button>
        </view>
      </view>
    </view>
    <view v-if="showConfirmDialog" class="custom-dialog-mask">
      <view class="custom-dialog">
        <view class="dialog-title">请确认提现信息</view>
        <view class="dialog-row">提现地址：{{ address }}</view>
        <view class="dialog-row">提现数量：{{ amount }} USDT</view>
        <view class="dialog-row">到账数量：{{ amount && fee !== null ? (Number(amount) - Number(fee)).toFixed(2) : 0 }} USDT</view>
        <view class="dialog-row">手续费：{{ fee }} USDT</view>
        <view class="dialog-actions">
          <button class="dialog-btn cancel" @click="showConfirmDialog=false">取消</button>
          <button class="dialog-btn confirm" @click="confirmWithdraw">确认提现</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
export default {
  data() {
    return {
      statusBarHeight: 0,
      navBarHeight: 50, // px
      navPaddingTop: 44, // 默认导航栏高度
      address: '',
      accountTypes: ['BEP20'],//, 'TRC20'
      accountTypeIndex: 0,
      amount: '',
      email: '',     
      payPwd: '',
      remark: '',
      withdrawParams: {
        minWithdraw: 0,
        maxWithdraw: 0,
        withdrawFee: 0,
        enableWithdraw: true
      },
      fee: 0,
      showConfirmDialog: false, // 新增：控制确认弹窗显示
      availableBalance: 0, // 新增：可用余额
    }
  },
  computed: {
    withdrawAmountPlaceholder() {
      // 动态placeholder
      return `单笔最小提现：大于${this.withdrawParams.minWithdraw}USDT`;
    },
    withdrawDesc() {
      // 动态说明
      return `单笔最小提现大于${this.withdrawParams.minWithdraw}USDT，单笔最大提现${this.withdrawParams.maxWithdraw}USDT。`;
    }
  },
  watch: {
    amount(val) {
      this.calcFee()
    }
  },
  created() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.navPaddingTop = this.statusBarHeight + this.navBarHeight
    this.getWithdrawParams()
    this.getUserInfo()
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    goToRecord() {
      uni.navigateTo({ url: '/pages/withdraw/record' })
    },
    sendCode() {
      uni.showToast({ title: '验证码已发送', icon: 'none' })
    },
    async getWithdrawParams() {
      try {
        const res = await request({
          url: '/api/sys/params/transfer-withdraw',
          method: 'GET'
        })
        if (res.code === 200 && res.data) {
          this.withdrawParams = res.data
        }
      } catch (e) {
        uni.showToast({ title: '获取参数失败', icon: 'none' })
      }
    },
    async getUserInfo() {
      try {
        const res = await request({
          url: '/api/user/info',
          method: 'GET'
        })
        if (res.code === 200 && res.data) {
          this.availableBalance = res.data.availableBalance
        }
      } catch (e) {
        uni.showToast({ title: '获取余额失败', icon: 'none' })
      }
    },
    calcFee() {
      const amt = Number(this.amount)
      const fee = Number(this.withdrawParams.withdrawFee || 0)
      if (!isNaN(amt) && !isNaN(fee)) {
        this.fee = fee
      } else {
        this.fee = 0
      }
    },
    submit() {
      if (!this.address) {
        uni.showToast({ title: '请输入提现地址', icon: 'none' })
        return
      }
      if (!this.amount) {
        uni.showToast({ title: '请输入提现数量', icon: 'none' })
        return
      }
      if (!this.payPwd) {
        uni.showToast({ title: '请输入支付密码', icon: 'none' })
        return
      }
      if (!this.withdrawParams.enableWithdraw) {
        uni.showToast({ title: '当前不允许提现', icon: 'none' })
        return
      }
      if (Number(this.amount) < Number(this.withdrawParams.minWithdraw)) {
        uni.showToast({ title: `最小提现金额为${this.withdrawParams.minWithdraw}`, icon: 'none' })
        return
      }
      if (Number(this.amount) > Number(this.withdrawParams.maxWithdraw)) {
        uni.showToast({ title: `最大提现金额为${this.withdrawParams.maxWithdraw}`, icon: 'none' })
        return
      }
      if (Number(this.amount) <= Number(this.fee)) {
        uni.showToast({ title: '金额需大于手续费', icon: 'none' })
        return
      }
      // 校验通过，弹出确认弹窗
      this.showConfirmDialog = true
    },
    // 新增：确认提现方法
    confirmWithdraw() {
      request({
        url: '/api/withdraw/create',
        method: 'POST',
        data: {
          amount: this.amount,
          address: this.address,
          chainName: this.accountTypes[this.accountTypeIndex],
          remark: this.remark,
          securityPassword: this.payPwd // 修改：字段名改为securityPassword
        }
      }).then(res => {
        if (res.code === 200) {
          uni.showToast({ title: '提现申请已提交', icon: 'success' })
          this.amount = ''
          this.address = ''
          this.remark = ''
          this.payPwd = ''
		    this.getUserInfo();
        } else {
          uni.showToast({ title: res.msg || '提现失败', icon: 'none' })
        }
        this.showConfirmDialog = false
      }).catch(e => {
        uni.showToast({ title: e.message, icon: 'none' })
        this.showConfirmDialog = false
      })
    },
    onAccountTypeChange(e) {
      const index = e.detail.value
      if (index === 1) { // TRC20
        uni.showToast({ title: 'TRC20提现功能开发中', icon: 'none' })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
  background: #121212 !important;
  display: flex;
  flex-direction: column;
  // padding-bottom: 40rpx; // 移除
}
.scroll-content {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  padding-bottom: 40rpx;
}
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #121212 !important;
}
.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.left-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
}
.right-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.page-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 30rpx !important; // 原34rpx
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.record-link {
  color: #fff;
  font-size: 24rpx; // 原28rpx
  font-weight: 500;
  margin-right: 32rpx;
}
.form-section {
  padding: 0;
}
.form-item {
  margin-bottom: 32rpx;
  display: flex;
  flex-direction: column;
  .label {
    color: #fff;
    font-size: 28rpx; // 原32rpx
    margin-bottom: 12rpx;
  }
  .input,
  .select,
  input,
  .form-item.row .input {
    background: #1E1E1E !important;
    border-radius: 8rpx;
    height: 56rpx;
    padding: 0 20rpx;
    font-size: 24rpx;
    color: #fff;
    border: none;
    outline: none;
    box-sizing: border-box;
  }
  .input.select {
    line-height: 2.25rem;
  }
  .input::placeholder,
  input::placeholder {
    color: #fff;
    opacity: 1;
    font-size: 24rpx; // 原28rpx
  }
  .input-tip {
    color: #FFD70099;
    font-size: 24rpx; // 原28rpx
    margin-top: 8rpx;
  }
  &.row {
    flex-direction: row;
    align-items: center;
    .input {
      flex: 1;
      margin-right: 16rpx;
    }
    .send-btn {
      background: transparent;
      color: #fff;
      font-size: 24rpx; // 原28rpx
      border-radius: 8rpx;
      padding: 0 24rpx;
      height: 64rpx;
      border: 1rpx solid #FFD700;
      font-weight: bold;
      transition: background 0.2s, color 0.2s;
    }
    .send-btn:active {
      background: #FFD700;
      color: #111;
    }
  }
}
.desc-section {
  padding: 0;
  margin-top: 12rpx;
  .desc-title {
    color: #fff;
    font-size: 26rpx; // 原30rpx
    font-weight: bold;
    margin-bottom: 8rpx;
    display: block;
  }
  .desc-text {
    color: #fff;
    font-size: 24rpx; // 原28rpx
    line-height: 1.6;
    white-space: pre-line;
  }
}
.result-action-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  margin-top: 32rpx;
}
.result-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  .result {
    color: #fff;
    font-size: 28rpx; // 原32rpx
    margin-bottom: 8rpx;
    display: block;
    text-align: left;
  }
}
.submit-btn {
  width: 200rpx;
  margin: 0 0 0 32rpx;
  height: 56rpx;
  background: #fff !important;
  color: #18191D !important;
  font-size: 24rpx;
  font-weight: bold;
  border-radius: 8rpx;
  border: none;
  box-shadow: 0 2rpx 8rpx #FFD70044;
  transition: background 0.2s, color 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.submit-btn:active {
  background: #eee !important;
  color: #18191D !important;
}
.card {
  background: #18191D !important;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx #FFD70011;
  margin: 0 24rpx 24rpx 24rpx;
  padding: 32rpx 24rpx 24rpx 24rpx;
}
.card .desc-section {
  background: #18191D !important;
  border-radius: 12rpx;
  padding: 24rpx;
}
.custom-dialog-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-dialog {
  background: #181818;
  border-radius: 16rpx;
  padding: 48rpx 32rpx 32rpx 32rpx;
  min-width: 520rpx;
  box-shadow: 0 8rpx 32rpx #000a;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.dialog-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
  text-align: center;
}
.dialog-row {
  color: #fff;
  font-size: 28rpx;
  margin-bottom: 16rpx;
}
.dialog-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 32rpx;
}
.dialog-btn {
  flex: 1;
  height: 64rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
  margin: 0 8rpx;
  background: #fff !important;
  color: #111 !important;
  border: none !important;
  font-weight: bold;
}
.dialog-btn.cancel {
}
.dialog-btn.confirm {
}
.available-text {
  color: #fff;
  font-size: 24rpx;
  margin-left: 16rpx;
  align-self: center;
}
.row-between {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
</style> 