import{H as a,n as t,e,s,f as o,h as c,w as i,i as r,o as l,j as n,m as u,z as d,t as f,p as h,l as p,q as m,F as _,k as g,x as b,y}from"./index-BnjgV7rC.js";import{_ as T}from"./uni-icons.9b9P_X39.js";import{r as v}from"./uni-app.es.BuhgQoIe.js";import{C as k}from"./custom-navbar.Bf8ZgRC_.js";import{r as x}from"./request.CNAVEKUC.js";import{_ as S}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const F=S({components:{CustomNavbar:k},data:()=>({statusBarHeight:0,fundAvailable:"0",fundFrozen:"0",fundTotal:"0",copyAvailable:"0",copyFrozen:"0",copyTotal:"0",activeTab:1,fundList:[],copyList:[],fundPage:1,copyPage:1,fundHasMore:!0,copyHasMore:!0,pageSize:10}),computed:{currentList(){return 1===this.activeTab?this.fundList:this.copyList},hasMore(){return 1===this.activeTab?this.fundHasMore:this.copyHasMore}},created(){const t=a();this.statusBarHeight=t.statusBarHeight,this.getUserInfo(),this.loadTradeRecords()},methods:{handleBack(){t()},onTransfer(){e({url:"/pages/transfer/index"})},onWithdraw(){e({url:"/pages/withdraw/index"})},goToRecord(){e({url:"/pages/account-transfer/record"})},async getUserInfo(){try{const a=await x({url:"/api/user/info",method:"GET"});200===a.code&&a.data&&(this.fundAvailable=a.data.availableBalance||"0",this.fundFrozen=a.data.frozenBalance||"0",this.fundTotal=(Number(this.fundAvailable)+Number(this.fundFrozen)).toFixed(2),this.copyAvailable=a.data.copyTradeBalance||"0",this.copyFrozen=a.data.copyTradeFrozenBalance||"0",this.copyTotal=(Number(this.copyAvailable)+Number(this.copyFrozen)).toFixed(2))}catch(a){this.fundAvailable=this.fundFrozen=this.fundTotal="0",this.copyAvailable=this.copyFrozen=this.copyTotal="0"}},switchTab(a){this.activeTab!==a&&(this.activeTab=a,0===this.currentList.length&&this.loadTradeRecords())},async loadTradeRecords(){const a=this.activeTab,t=1===this.activeTab?this.fundPage:this.copyPage;try{const e=await x({url:"/api/trade-record/list",method:"GET",params:{page:t,size:this.pageSize,accountType:a}});if(200===e.code&&e.data){const a=e.data.records||[],s=e.data.current<e.data.pages;1===this.activeTab?(this.fundList=1===t?a:[...this.fundList,...a],this.fundHasMore=s):(this.copyList=1===t?a:[...this.copyList,...a],this.copyHasMore=s)}}catch(e){console.error("加载交易记录失败:",e),s({title:"加载失败",icon:"none"})}},loadMore(){1===this.activeTab?this.fundPage++:this.copyPage++,this.loadTradeRecords()},formatTime(a){if(!a)return"";const t=new Date(a);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},formatTime1(a){if(!a)return"";const t=new Date(a),e=new Date-t;if(e<6e4)return"刚刚";if(e<36e5)return Math.floor(e/6e4)+"分钟前";if(e<864e5)return Math.floor(e/36e5)+"小时前";return`${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`}}},[["render",function(a,t,e,s,k,x){const S=v(o("uni-icons"),T),F=r,w=b,z=y;return l(),c(F,{class:"container"},{default:i((()=>[n(F,{class:"custom-navbar",style:d({paddingTop:k.statusBarHeight+"px"})},{default:i((()=>[n(F,{class:"navbar-content"},{default:i((()=>[n(F,{class:"left-area",onClick:x.handleBack},{default:i((()=>[n(S,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),n(w,{class:"page-title"},{default:i((()=>[u("我的资产")])),_:1}),n(F,{class:"right-area"},{default:i((()=>[n(w,{class:"record-link",onClick:x.goToRecord},{default:i((()=>[u("划转记录")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["style"]),n(F,{class:"asset-header"},{default:i((()=>[n(F,{class:"account-summary"},{default:i((()=>[n(F,{class:"account-block"},{default:i((()=>[n(w,{class:"account-title"},{default:i((()=>[u("资金账户")])),_:1}),n(F,{class:"account-row"},{default:i((()=>[n(w,null,{default:i((()=>[u("总余额：")])),_:1}),n(w,{class:"account-value"},{default:i((()=>[u(f(Number(k.fundTotal).toFixed(4))+"USDT",1)])),_:1})])),_:1}),n(F,{class:"account-row"},{default:i((()=>[n(w,null,{default:i((()=>[u("冻结：")])),_:1}),n(w,{class:"account-value"},{default:i((()=>[u(f(Number(k.fundFrozen).toFixed(2)),1)])),_:1})])),_:1})])),_:1}),n(F,{class:"account-block"},{default:i((()=>[n(w,{class:"account-title"},{default:i((()=>[u("跟单账户")])),_:1}),n(F,{class:"account-row"},{default:i((()=>[n(w,null,{default:i((()=>[u("总余额：")])),_:1}),n(w,{class:"account-value"},{default:i((()=>[u(f(Number(k.copyTotal).toFixed(2)),1)])),_:1})])),_:1})])),_:1})])),_:1}),n(F,{class:"btn-group"},{default:i((()=>[n(z,{class:"asset-btn",onClick:x.onTransfer},{default:i((()=>[u("互转")])),_:1},8,["onClick"]),n(z,{class:"asset-btn",onClick:x.onWithdraw},{default:i((()=>[u("提现")])),_:1},8,["onClick"])])),_:1})])),_:1}),n(F,{class:"transaction-card"},{default:i((()=>[n(F,{class:"section-title-row"},{default:i((()=>[n(w,{class:"section-title"},{default:i((()=>[u("我的交易")])),_:1})])),_:1}),n(F,{class:"tab-container"},{default:i((()=>[n(F,{class:h(["tab-item",{active:1===k.activeTab}]),onClick:t[0]||(t[0]=a=>x.switchTab(1))},{default:i((()=>[n(w,{class:"tab-text"},{default:i((()=>[u("资金账户")])),_:1})])),_:1},8,["class"]),n(F,{class:h(["tab-item",{active:2===k.activeTab}]),onClick:t[1]||(t[1]=a=>x.switchTab(2))},{default:i((()=>[n(w,{class:"tab-text"},{default:i((()=>[u("跟单账户")])),_:1})])),_:1},8,["class"])])),_:1}),x.currentList.length>0?(l(),c(F,{key:0,class:"transaction-list"},{default:i((()=>[(l(!0),p(_,null,m(x.currentList,((a,t)=>(l(),c(F,{class:"transaction-item",key:a.id},{default:i((()=>[n(F,{class:"transaction-left"},{default:i((()=>[n(w,{class:"transaction-type"},{default:i((()=>[u(f(a.tradeType),1)])),_:2},1024),n(w,{class:"transaction-time"},{default:i((()=>[u(f(x.formatTime(a.createTime)),1)])),_:2},1024)])),_:2},1024),n(F,{class:"transaction-right"},{default:i((()=>[n(w,{class:h(["transaction-amount",{income:a.amount>0,expense:a.amount<0}])},{default:i((()=>[u(f(a.amount>0?"+":"")+f(a.amount),1)])),_:2},1032,["class"])])),_:2},1024)])),_:2},1024)))),128)),x.hasMore?(l(),c(F,{key:0,class:"load-more",onClick:x.loadMore},{default:i((()=>[n(w,{class:"load-more-text"},{default:i((()=>[u("加载更多")])),_:1})])),_:1},8,["onClick"])):g("",!0)])),_:1})):(l(),c(F,{key:1,class:"empty-box"},{default:i((()=>[n(w,{class:"empty-text"},{default:i((()=>[u("暂无交易记录")])),_:1})])),_:1}))])),_:1})])),_:1})}],["__scopeId","data-v-3ac87207"]]);export{F as default};
