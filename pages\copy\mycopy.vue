<template>
  <view class="mycopy-container">
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">我的跟单</text>
        <view class="right-area"></view>
      </view>
    </view>
    <view class="mycopy-top-card">
      <view class="top-row">
        <view class="top-label">未实现盈亏</view>
        <view class="top-value" :class="getTotalUnrealizedProfit() > 0 ? 'profit' : (getTotalUnrealizedProfit() < 0 ? 'loss' : '')">{{ getTotalUnrealizedProfit().toFixed(4) }} USDT</view>
      </view>
      <view class="bottom-row">
        <view class="bottom-col">
          <view class="bottom-label">跟单净利润</view>
          <view class="bottom-value">{{ (Number(totalProfit)||0).toFixed(4) }} USDT</view>
        </view>
        <view class="bottom-col">
          <view class="bottom-label">可用余额</view>
          <view class="bottom-value">{{ (Number(balance)||0).toFixed(4) }} USDT</view>
        </view>
      </view>
    </view>
    <view class="mycopy-tabs">
      <view
        class="mycopy-tab"
        :class="{active: tabIndex === 0}"
        @click="tabIndex = 0"
      >当前持仓</view>
      <view
        class="mycopy-tab"
        :class="{active: tabIndex === 1}"
        @click="tabIndex = 1"
      >历史跟单</view>
    </view>
    <view class="mycopy-tab-content">
      <!-- 订单卡片区域保持不变 -->
      <template v-if="tabIndex === 0">
        <view v-if="todayList.length === 0" class="mycopy-empty">暂无数据</view>
        <view v-else>
          <view v-for="item in todayList" :key="item.id" class="order-card">
            <view class="order-header">
              <view class="symbol-info">
                <text class="symbol">{{ formatSymbol(item.symbol) }}</text>
                <text class="direction" :class="item.direction === 1 ? 'direction-up' : 'direction-down'">
                  {{ item.direction === 1 ? '买涨' : '买跌' }}
                </text>
              </view>
              <text class="status" :class="item.status == 1 ? 'status-holding' : 'status-closed'">
                {{ item.status == 1 ? '持仓中' : '已平仓' }}
              </text>
            </view>
            <view class="order-main-info">
              <!-- 第一行：持仓、盈利、杠杆 -->
              <view class="order-row-horizontal">
                <view class="order-item">
                  <text class="label">持仓({{ item.symbol ? item.symbol.split('/')[0] : '' }})</text>
                  <text class="value">{{ Number(item.positionAmount).toFixed(4) }}</text>
                </view>
                <view class="order-item">
                  <text class="label">盈利率</text>
                  <text class="value" :class="getRealTimeProfitRateValue(item) > 0 ? 'profit' : (getRealTimeProfitRateValue(item) < 0 ? 'loss' : '')">
                    {{ getRealTimeProfitRate(item) }}
                  </text>
                </view>
                <view class="order-item">
                  <text class="label">杠杆</text>
                  <text class="value">{{ item.lever }}x</text>
                </view>
              </view>
              <!-- 第二行：保证金、开仓价 -->
              <view class="order-row-horizontal">
                <view class="order-item">
                  <text class="label">保证金(USDT)</text>
                  <text class="value">{{Number( item.marginAmount).toFixed(4) }}</text>
                </view>
                <view class="order-item">
                  <text class="label">开仓价(USDT)</text>
                  <text class="value">{{ item.openPrice }}</text>
                </view>
                <view class="order-item">
                  <!-- 空白占位 -->
                </view>
              </view>
            </view>
            <view class="order-footer">
              <view class="order-row">
                <text class="label">开仓</text>
                <text class="value">{{ formatDate(item.openTime) }}</text>
              </view>
              <view v-if="item.closeTime" class="order-row">
                <text class="label">平仓</text>
                <text class="value">{{ formatDate(item.closeTime) }}</text>
              </view>
            </view>
          </view>
        </view>
        <view v-if="todayList.length > 0" class="pagination">
          <button v-if="!todayFinished && !todayLoading" @click="loadOrderList('today', todayPage + 1, true)">加载更多</button>
          <text v-if="todayFinished" class="no-more">没有更多了</text>
          <text v-if="todayLoading" class="loading">加载中...</text>
        </view>
      </template>
      <template v-else>
        <view v-if="historyList.length === 0" class="mycopy-empty">暂无数据</view>
        <view v-else>
          <view v-for="item in historyList" :key="item.id" class="order-card">
            <view class="order-header">
              <view class="symbol-info">
                <text class="symbol">{{ formatSymbol(item.symbol) }}</text>
                <text class="direction" :class="item.direction === 1 ? 'direction-up' : 'direction-down'">
                  {{ item.direction === 1 ? '买涨' : '买跌' }}
                </text>
              </view>
              <text class="status" :class="item.status == 1 ? 'status-holding' : 'status-closed'">
                {{ item.status == 1 ? '持仓中' : '已平仓' }}
              </text>
            </view>
            <view class="order-main-info">
              <!-- 第一行：持仓、盈利、杠杆 -->
              <view class="order-row-horizontal">
                <view class="order-item">
                  <text class="label">持仓({{ item.symbol ? item.symbol.split('/')[0] : '' }})</text>
                  <text class="value">{{ Number(item.positionAmount).toFixed(4) }}</text>
                </view>
                <view class="order-item">
                  <text class="label">盈利(USDT)</text>
                  <text class="value" :class="getRealTimeProfit(item) > 0 ? 'profit' : (getRealTimeProfit(item) < 0 ? 'loss' : '')">
                    {{ getRealTimeProfit(item) }}
                  </text>
                </view>
                <view class="order-item">
                  <text class="label">杠杆</text>
                  <text class="value">{{ item.lever }}x</text>
                </view>
              </view>
              <!-- 第二行：保证金、开仓价、平仓价 -->
              <view class="order-row-horizontal">
                <view class="order-item">
                  <text class="label">保证金(USDT)</text>
                  <text class="value">{{ item.marginAmount }}</text>
                </view>
                <view class="order-item">
                  <text class="label">开仓价(USDT)</text>
                  <text class="value">{{ item.openPrice }}</text>
                </view>
                <view class="order-item">
                  <text class="label">平仓价(USDT)</text>
                  <text class="value">{{ item.closePrice || '--' }}</text>
                </view>
              </view>
            </view>
            <view class="order-footer">
              <view class="order-row">
                <text class="label">开仓</text>
                <text class="value">{{ formatDate(item.openTime) }}</text>
              </view>
              <view v-if="item.closeTime" class="order-row">
                <text class="label">平仓</text>
                <text class="value">{{ formatDate(item.closeTime) }}</text>
              </view>
            </view>
          </view>
        </view>
        <view v-if="historyList.length > 0" class="pagination">
          <button v-if="!historyFinished && !historyLoading" @click="loadOrderList('history', historyPage + 1, true)">加载更多</button>
          <text v-if="historyFinished" class="no-more">没有更多了</text>
          <text v-if="historyLoading" class="loading">加载中...</text>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
import config from '@/config/index.js'
import request from '@/utils/request.js'
export default {
  name: 'MyCopy',
  data() {
    return {
      baseURL: config.apiBaseUrl,
      tabIndex: 0,
      balance: 0,
      totalProfit: 0,
      unrealizedProfit: 0,
      todayList: [],
      todayPage: 1,
      todayPageSize: 10,
      todayTotal: 0,
      todayLoading: false,
      todayFinished: false,
      historyList: [],
      historyPage: 1,
      historyPageSize: 10,
      historyTotal: 0,
      historyLoading: false,
      historyFinished: false,
      statusBarHeight: 0,
      holdProfitSSE: null // SSE 连接对象
    }
  },
  onShow() {
    console.log('mycopy页面显示，开始加载数据...');
    this.loadUserInfo();
    this.loadProfitSummary();
    this.loadOrderList('today');
    this.loadOrderList('history');
    // 初始化实时盈利更新
    this.initHoldProfitSSE();
  },
  onHide() {
    // 页面隐藏时关闭 SSE 连接
    if (this.holdProfitSSE) {
      this.holdProfitSSE.close();
      this.holdProfitSSE = null;
    }
  },
  methods: {
    handleBack() {
      uni.switchTab({ url: '/pages/robot/index' });
    },
    async loadUserInfo() {
      const res = await request({ url: '/api/user/info', method: 'GET' });
      this.balance = res?.data?.copyTradeBalance || 0;
    },
    async loadProfitSummary() {
      const res = await request({ url: '/api/copy/profit/summary', method: 'GET' });
      this.totalProfit = res?.data?.total_profit || 0;
    },
    async loadOrderList(type, page = 1, append = false) {
      const pageSize = type === 'today' ? this.todayPageSize : this.historyPageSize;
      if (type === 'today') this.todayLoading = true;
      else this.historyLoading = true;
      const res = await request({
        url: '/api/copy/order/list',
        method: 'GET',
        data: { type, page, pageSize }
      });
      const list = res?.data?.list || [];
      const total = res?.data?.total || 0;
      console.log(`加载${type}订单列表:`, list);
      console.log(`订单ID列表:`, list.map(item => item.id));

      if (type === 'today') {
        // 当前持仓订单（后端已按status=1筛选，无需前端过滤）
        console.log(`当前持仓数据${list.length}条`);

        this.todayTotal = total;
        this.todayPage = page;
        if (append) {
          this.todayList = this.todayList.concat(list);
        } else {
          this.todayList = list;
        }
        this.todayFinished = this.todayList.length >= total;
        this.todayLoading = false;
        console.log('当前持仓列表更新后:', this.todayList.map(item => ({ id: item.id, symbol: item.symbol, status: item.status })));
      } else {
        this.historyTotal = total;
        this.historyPage = page;
        if (append) {
          this.historyList = this.historyList.concat(list);
        } else {
          this.historyList = list;
        }
        this.historyFinished = this.historyList.length >= total;
        this.historyLoading = false;
      }
    },
    formatDate(val) {
      if (!val) return '-';
      const date = typeof val === 'string' ? new Date(val) : val;
      if (isNaN(date.getTime())) return '-';
      const y = date.getFullYear();
      const m = (date.getMonth() + 1).toString().padStart(2, '0');
      const d = date.getDate().toString().padStart(2, '0');
      const h = date.getHours().toString().padStart(2, '0');
      const min = date.getMinutes().toString().padStart(2, '0');
      const s = date.getSeconds().toString().padStart(2, '0');
      return `${y}-${m}-${d} ${h}:${min}:${s}`;
    },
    formatSymbol(symbol) {
      if (!symbol) return '--';
      // 移除 USDT 后缀，显示更简洁的交易对名称
      return symbol.replace('/USDT', '').replace('USDT', '');
    },
    // 获取实时盈利（简化版本）
    getRealTimeProfit(item) {
      // 优先使用实时推送的数据
      if (item.realTimeProfit !== undefined) {
        return Number(item.realTimeProfit).toFixed(4);
      }
      // 否则使用原始数据
      return Number(item.profit || 0).toFixed(4);
    },
    // 获取实时盈利比例
    getRealTimeProfitRate(item) {
      // 优先使用实时推送的数据
      if (item.realTimeProfitRate !== undefined) {
        return Number(item.realTimeProfitRate).toFixed(3) + '%';
      }
      // 否则使用原始数据
      return Number(item.profitRate || 0).toFixed(3) + '%';
    },
    // 获取实时盈利比例的数值（用于颜色判断）
    getRealTimeProfitRateValue(item) {
      // 优先使用实时推送的数据
      if (item.realTimeProfitRate !== undefined) {
        return Number(item.realTimeProfitRate);
      }
      // 否则使用原始数据
      return Number(item.profitRate || 0);
    },
    // 计算总未实现盈亏
    getTotalUnrealizedProfit() {
      let total = 0;
      // 计算当前持仓的未实现盈亏
      this.todayList.forEach(item => {
        if (item.status === 1) { // 只计算持仓中的订单
          total += Number(item.realTimeProfit !== undefined ? item.realTimeProfit : (item.profit || 0));
        }
      });
      // 计算历史跟单中仍在持仓的未实现盈亏
      this.historyList.forEach(item => {
        if (item.status === 1) { // 只计算持仓中的订单
          total += Number(item.realTimeProfit !== undefined ? item.realTimeProfit : (item.profit || 0));
        }
      });
      return total;
    },
     // 计算总未实现盈亏
    getTotalUnrealizedProfitRaido() {
      let total = 0;
      // 计算当前持仓的未实现盈亏
      this.todayList.forEach(item => {
        if (item.status === 1) { // 只计算持仓中的订单
          total += Number(item.profitRate !== undefined ? item.profitRate : (item.profitRate || 0));
        }
      });
      // 计算历史跟单中仍在持仓的未实现盈亏
      this.historyList.forEach(item => {
        if (item.status === 1) { // 只计算持仓中的订单
          total += Number(item.profitRate !== undefined ? item.profitRate : (item.profitRate || 0));
        }
      });
      return total;
    },
    // 初始化持仓盈利 SSE 连接（参考 leader 页面）
    initHoldProfitSSE() {
      if (this.holdProfitSSE) this.holdProfitSSE.close();
      const token = uni.getStorageSync('token') || '';
      if (!token) {
        console.log('没有token，无法建立SSE连接');
        return;
      }

      const sseUrl = this.baseURL + '/api/copy/order/hold/profit/stream?token=' + encodeURIComponent(token);
      console.log('建立SSE连接:', sseUrl);

      this.holdProfitSSE = new window.EventSource(sseUrl);

      this.holdProfitSSE.onopen = () => {
        console.log('SSE连接已建立');
      };

      this.holdProfitSSE.onerror = (error) => {
        console.error('SSE连接错误:', error);
      };

      // 监听 profit-update 事件
      this.holdProfitSSE.addEventListener('profit-update', async (event) => {
        console.log('收到SSE原始事件:', event);
        let data = event.data;
        console.log('SSE原始数据:', data);
        if (data.startsWith('data: ')) data = data.slice(6);
        console.log('处理后的数据:', data);
        try {
          const arr = JSON.parse(data);
          console.log('解析后的持仓盈利数据:', arr);
          console.log('当前持仓列表:', this.todayList.map(item => ({ id: item.id, symbol: item.symbol })));
          console.log('当前历史列表:', this.historyList.map(item => ({ id: item.id, symbol: item.symbol })));

          let removed = false;
          let removedCount = 0;
          let updated = false;
          let hasClosedOrders = false; // 标记是否有已平仓订单

          arr.forEach(item => {
            console.log('处理订单数据:', item);

            // 检查是否有已平仓订单
            if (item.status === 2) {
              hasClosedOrders = true;
            }

            // 更新当前持仓列表
            const todayIdx = this.todayList.findIndex(o => o.id === item.orderId);
            console.log(`在持仓列表中查找订单${item.orderId}，索引:`, todayIdx);

            if (todayIdx !== -1) {
              if (item.status === 1) {
                // 持仓中状态，更新实时盈利数据
                console.log(`更新持仓订单${item.orderId}的实时盈利:`, item.profit);
                this.todayList[todayIdx].realTimeProfit = item.profit;
                this.todayList[todayIdx].realTimeProfitRate = item.profitRate;
                this.todayList[todayIdx].currentPrice = item.currentPrice;
                this.todayList[todayIdx].status = item.status;
                updated = true;
              } else {
                // 非持仓状态（已平仓、已取消等），从持仓列表中移除
                this.todayList.splice(todayIdx, 1);
                removed = true;
                removedCount++;
                console.log(`订单${item.orderId}状态变为${item.status}，从持仓列表中移除`);
              }
            }

            // 更新历史跟单列表
            const historyIdx = this.historyList.findIndex(o => o.id === item.orderId);
            console.log(`在历史列表中查找订单${item.orderId}，索引:`, historyIdx);

            if (historyIdx !== -1) {
              if (item.status === 1) {
                // 持仓中状态，更新实时盈利数据
                console.log(`更新历史订单${item.orderId}的实时盈利:`, item.profit);
                this.historyList[historyIdx].realTimeProfit = item.profit;
                this.historyList[historyIdx].realTimeProfitRate = item.profitRate;
                this.historyList[historyIdx].currentPrice = item.currentPrice;
                this.historyList[historyIdx].status = item.status;
                updated = true;
              } else {
                // 非持仓状态，更新状态但保留在历史列表中
                console.log(`历史订单${item.orderId}状态变为${item.status}，更新状态`);
                this.historyList[historyIdx].status = item.status;
                // 清除实时盈利数据，使用最终盈利
                this.historyList[historyIdx].realTimeProfit = undefined;
                this.historyList[historyIdx].realTimeProfitRate = undefined;
                updated = true;
              }
            }
          });

          if (updated) {
            console.log('有数据更新，强制刷新页面显示');
            this.$forceUpdate();
          }

          if (removed) {
            console.log(`共移除${removedCount}个非持仓订单`);
            // // 显示用户提示
            // if (removedCount === 1) {
            //   uni.showToast({
            //     title: '订单状态已更新',
            //     icon: 'success',
            //     duration: 2000
            //   });
            // } else if (removedCount > 1) {
            //   uni.showToast({
            //     title: `${removedCount}个订单状态已更新`,
            //     icon: 'success',
            //     duration: 2000
            //   });
            // }
            await this.loadUserInfo(); // 刷新余额
            await this.loadProfitSummary(); // 刷新汇总数据
          }

          // 如果有已平仓订单，刷新历史跟单数据
          if (hasClosedOrders) {
            console.log('检测到已平仓订单，刷新历史跟单数据');
            this.historyPage = 1; // 重置页码
            this.historyFinished = false; // 重置分页状态
            await this.loadOrderList('history', 1, false); // 重新加载历史数据
          }
        } catch (e) {
          console.error('处理实时盈利数据失败:', e);
        }
      });

      this.holdProfitSSE.onerror = () => {
        this.holdProfitSSE.close();
        setTimeout(() => this.initHoldProfitSSE(), 5000);
      };
    },

  }
}
</script>

<style scoped>
.mycopy-container {
  min-height: calc(100vh - 66px);
  background: #121212;
  padding: 44px 0 32rpx 0;
}
.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #121212 !important;
    .navbar-content {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;
      .left-area {
        width: 80rpx;
        height: 44px;
        display: flex;
        align-items: center;
        &:active {
          opacity: 0.7;
        }
      }
      .page-title {
        color: #fff;
        font-size: 30rpx !important;
        font-weight: 500;
      }
      .right-area {
        width: 80rpx;
      }
    }
  }
.mycopy-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  padding: 32rpx 0 18rpx 0;
}
.mycopy-top-card {
  background: #18191D;
  border-radius: 20rpx;
  margin: 0 24rpx 24rpx 24rpx;
  padding: 24rpx 24rpx 18rpx 24rpx;
  position: relative;
  overflow: hidden;
}
.top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18rpx;
}
.top-label {
  color: #bbb;
  font-size: 24rpx;
}
.top-value {
  color: #5ecfff;
  font-size: 36rpx;
  font-weight: bold;
}
.top-value.profit {
  color: #00C566 !important;
}
.top-value.loss {
  color: #FF4D4F !important;
}
.bottom-row {
  display: flex;
  justify-content: space-between;
  margin-top: 8rpx;
}
.bottom-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.bottom-label {
  color: #bbb;
  font-size: 20rpx;
  margin-bottom: 4rpx;
}
.bottom-value {
  color: #fff;
  font-size: 26rpx;
  font-weight: bold;
}
.bottom-col + .bottom-col {
  align-items: flex-end;
}
.mycopy-tabs {
  display: flex;
  margin: 0 24rpx;
  border-bottom: 2rpx solid #222;
  margin-bottom: 12rpx;
}
.mycopy-tab {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #bbb;
  padding: 18rpx 0 12rpx 0;
  position: relative;
  cursor: pointer;
}
.mycopy-tab.active {
  color: #fff;
  font-weight: bold;
  border-bottom: 4rpx solid #fff;
}
.mycopy-tab-content {
  min-height: 300rpx;
  padding: 32rpx 0 0 0;
}
.mycopy-empty {
  color: #bbb;
  font-size: 24rpx;
  text-align: center;
  margin-top: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-img {
  width: 120rpx;
  margin-bottom: 18rpx;
  opacity: 0.7;
}
body {
  background: #121212 !important;
}
.order-card {
  background: #18191D;
  border-radius: 16rpx;
  margin: 0 16rpx 24rpx 16rpx;
  padding: 24rpx;
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  padding-bottom: 8rpx;
  border-bottom: 1px solid #333;
}

.symbol-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.symbol {
  color: #5ecfff;
  font-size: 28rpx;
  font-weight: bold;
}

.direction {
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

.direction-up {
  color: #fff;
  background: #00C566;
}

.direction-down {
  color: #fff;
  background: #FF4D4F;
}

.status-holding {
  color: #5ecfff;
}

.status-closed {
  color: #bbb;
}
.order-main-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 12rpx;
}
.order-row-horizontal {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.order-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4rpx;
}
.order-item .label {
  color: #bbb;
  font-size: 22rpx;
  margin-right: 0;
}
.order-item .value {
  color: #fff;
  font-weight: 500;
  font-size: 24rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.order-main-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.order-main-col + .order-main-col {
  margin-left: 32rpx;
}
.order-footer {
  margin-top: 12rpx;
  border-top: 1rpx solid #333;
  padding-top: 12rpx;
  display: flex;
  justify-content: flex-start;
  gap: 32rpx;
  flex-wrap: wrap;
}
.order-footer .order-row {
  flex: 0 0 auto;
  margin-bottom: 0;
  min-width: 200rpx;
}
.order-footer .value {
  font-weight: normal;
  white-space: normal;
  overflow: visible;
  text-overflow: unset;
  font-size: 22rpx;
}
.order-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 24rpx;
}
.label {
  color: #bbb;
  margin-right: 16rpx;
  min-width: fit-content;
  flex-shrink: 0;
}
.value {
  color: #fff;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.value.profit {
  color: #00C566 !important;
}
.value.loss {
  color: #FF4D4F !important;
}
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 16rpx 0 0 0;
  gap: 16rpx;
}
.pagination button {
  background: #222;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 24rpx;
  font-size: 24rpx;
}
.pagination button:disabled {
  background: #444;
  color: #aaa;
}
.no-more {
  color: #888;
  font-size: 22rpx;
}
.loading {
  color: #5ecfff;
  font-size: 22rpx;
}
</style>
  