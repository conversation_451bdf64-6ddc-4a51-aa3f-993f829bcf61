<template>
  <view class="profit-container" :style="{ paddingTop: navPaddingTop + 'px' }">
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">佣金/收益</text>
        <view class="right-area"></view>
      </view>
    </view>
    <!-- 收益统计卡片 -->
    <view class="profit-summary">
      <view class="summary-row">
        <view class="summary-col">
          <text class="summary-label">累积收益(USDT)</text>
          <text class="summary-value">{{ summaryData.totalProfit || 0 }}</text>
        </view>
        <view class="summary-col">
          <text class="summary-label">累计佣金(USDT)</text>
          <text class="summary-value">{{ summaryData.totalCommission || 0 }}</text>
        </view>
      </view>

    </view>
    <view class="tab-bar">
      <view class="tab" :class="{ active: activeTab === 0 }" @click="onTabChange(0)">收益记录</view>
      <view class="tab" :class="{ active: activeTab === 1 }" @click="onTabChange(1)">佣金记录</view>
    </view>
    <!-- 收益记录 -->
    <view v-if="activeTab === 0" class="record-card">
      <view v-for="(item, idx) in profitList" :key="item.id || item.userNo" class="record-item">
        <text class="record-index">{{ idx + 1 }}</text>
        <text class="record-amount">{{ item.commissionAmount || 0 }}</text>
        <text class="record-time">{{ formatTime(item.createTime) }}</text>
      </view>
      <view v-if="loading" class="loading-text">加载中...</view>
      <view v-if="!loading && profitList.length === 0" class="empty-text">暂无数据</view>
      <view v-if="!loading && profitList.length < profitTotal" class="load-more" @click="loadMore">加载更多</view>
    </view>

    <!-- 佣金记录 -->
    <view v-if="activeTab === 1" class="record-card">
      <view v-for="(item, idx) in commissionList" :key="item.id || item.userNo" class="record-item">
        <text class="record-index">{{ idx + 1 }}</text>
        <text class="record-amount">{{ item.commissionAmount || 0 }}</text>
        <text class="record-time">{{ formatTime(item.createTime) }}</text>
      </view>
      <view v-if="loading" class="loading-text">加载中...</view>
      <view v-if="!loading && commissionList.length === 0" class="empty-text">暂无数据</view>
      <view v-if="!loading && commissionList.length < commissionTotal" class="load-more" @click="loadMore">加载更多</view>
    </view>
    <!-- 空状态 -->
    <!-- <view class="empty-state">
      <view class="empty-img">
        <svg width="180" height="180" viewBox="0 0 180 180">
          <rect x="40" y="80" width="100" height="60" rx="8" fill="#d6ff3c" />
          <rect x="60" y="60" width="60" height="40" rx="8" fill="#d6ff3c" opacity="0.7"/>
        </svg>
      </view>
      <text class="empty-text">空空如也</text>
    </view> -->
  </view>
</template>

<script>
import request from '@/utils/request.js'
export default {
  data() {
    return {
      statusBarHeight: 0,
      navBarHeight: 44,
      navPaddingTop: 44,
      activeTab: 0, // 当前tab
      commissionList: [], // 佣金记录
      profitList: [], // 收益记录
      loading: false,
      commissionTotal: 0,
      profitTotal: 0,
      commissionPage: 1,
      profitPage: 1,
      pageSize: 10,
      summaryData: { // 汇总数据
        totalCommission: 0,
        totalProfit: 0
      }
    }
  },
  created() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.navPaddingTop = this.statusBarHeight + this.navBarHeight
    this.getSummaryData()
    this.getProfitList(true)
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    async getSummaryData() {
      try {
        const res = await request({
          url: '/api/commission/summary',
          method: 'GET'
        })
        if (res.code === 200 && res.data) {
          this.summaryData = res.data
        }
      } catch (error) {
        console.error('获取汇总数据失败:', error)
      }
    },
    onTabChange(idx) {
      this.activeTab = idx
      if (idx === 0) {
        this.getProfitList(true)
      } else {
        this.getCommissionList(true)
      }
    },
    async getCommissionList(reset = false) {
      this.loading = true
      if (reset) this.commissionPage = 1
      try {
        const res = await request({
          url: '/api/commission/list',
          method: 'GET',
          data: {
            page: this.commissionPage,
            size: this.pageSize,
            types: 1
          }
        })
        if (res.code === 200 && res.data) {
          this.commissionList = reset ? res.data.records : this.commissionList.concat(res.data.records)
          this.commissionTotal = res.data.total
        }
      } finally {
        this.loading = false
      }
    },
    async getProfitList(reset = false) {
      this.loading = true
      if (reset) this.profitPage = 1
      try {
        const res = await request({
          url: '/api/commission/list',
          method: 'GET',
          data: {
            page: this.profitPage,
            size: this.pageSize,
            types: 2
          }
        })
        if (res.code === 200 && res.data) {
          this.profitList = reset ? res.data.records : this.profitList.concat(res.data.records)
          this.profitTotal = res.data.total
        }
      } finally {
        this.loading = false
      }
    },
    loadMore() {
      if (this.loading) return
      if (this.activeTab === 0) {
        if (this.profitList.length < this.profitTotal) {
          this.profitPage++
          this.getProfitList()
        }
      } else {
        if (this.commissionList.length < this.commissionTotal) {
          this.commissionPage++
          this.getCommissionList()
        }
      }
    },
    formatTime(val) {
      if (!val) return ''
      const d = new Date(val)
      return `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}-${d.getDate().toString().padStart(2,'0')} ${d.getHours().toString().padStart(2,'0')}:${d.getMinutes().toString().padStart(2,'0')}`
    },
  }
}
</script>

<style lang="scss" scoped>
.profit-container {
  min-height: 100vh;
  background: #121212;
}
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #121212;
}
.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.left-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
}
.right-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.page-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.profit-summary {
  background: #18191D;
  border-radius: 18rpx;
  margin: 40rpx 32rpx 24rpx 32rpx;
  padding: 32rpx 24rpx 24rpx 24rpx;
  box-shadow: 0 2rpx 8rpx #FFD70022;
  .summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 18rpx;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .summary-col {
    flex: 1;
    text-align: center;
    .summary-label {
      color: #fff;
      font-size: 28rpx;
      margin-bottom: 12rpx;
      display: block;
    }
    .summary-value {
      color: #fff;
      font-size: 28rpx;
      font-weight: bold;
      display: block;
    }
  }
  .transfer-btn {
    width: 100%;
    margin-top: 24rpx;
    height: 80rpx;
    background: #d6ff3c;
    color: #222;
    font-size: 32rpx;
    font-weight: bold;
    border-radius: 8rpx;
    border: none;
    box-shadow: 0 2rpx 8rpx #FFD70044;
    transition: background 0.2s, color 0.2s;
  }
  .transfer-btn:active {
    background: #FFD700;
    color: #111;
  }
}
.filter-bar {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 32rpx;
  margin-bottom: 12rpx;
  .filter-item {
    color: #FFD70099;
    font-size: 28rpx;
    margin-right: 32rpx;
  }
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 120rpx;
  .empty-img {
    margin-bottom: 32rpx;
  }
  .empty-text {
    color: #fff;
    font-size: 28rpx;
  }
}
.tab-bar {
  display: flex;
  background: #121212;
  border-radius: 12rpx;
  margin: 24rpx 24rpx 0 24rpx;
  padding: 0 8rpx;
  .tab {
    flex: 1;
    text-align: center;
    padding: 18rpx 0;
    font-size: 28rpx;
    color: #fff;
    background: #121212;
    border-bottom: 4rpx solid transparent;
    transition: color 0.2s, border-color 0.2s, background 0.2s;
    &:first-child {
      border-top-left-radius: 12rpx;
      border-bottom-left-radius: 12rpx;
    }
    &:last-child {
      border-top-right-radius: 12rpx;
      border-bottom-right-radius: 12rpx;
    }
    &.active {
      color: #111;
      background: #fff;
      font-weight: bold;
      border-bottom: 4rpx solid #fff;
    }
  }
}
.record-card {
  background: #18191D;
  border-radius: 18rpx;
  margin: 0 24rpx 24rpx 24rpx;
  padding: 0 24rpx 24rpx 24rpx;
  box-shadow: 0 2rpx 8rpx #FFD70022;
  max-height: 60vh;
  overflow-y: auto;
}
.record-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1px solid #232323;
  color: #fff;
  .record-index {
    width: 80rpx;
    text-align: center;
    font-size: 26rpx;
    color: #999;
    font-weight: bold;
  }
  .record-amount {
    flex: 1;
    text-align: center;
    font-size: 26rpx;
    font-weight: bold;
    color: #02BF87;
  }
  .record-time {
    flex: 1;
    text-align: right;
    font-size: 26rpx;
    color: #999;
  }
}
.loading-text, .empty-text, .load-more {
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  padding: 32rpx 0;
}
.load-more {
  cursor: pointer;
}
.record-commission.clickable {
  cursor: pointer;
  color: #d6ff3c;
  text-decoration: underline;
}
</style> 