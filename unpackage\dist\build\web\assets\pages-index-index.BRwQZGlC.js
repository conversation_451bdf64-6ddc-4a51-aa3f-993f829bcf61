import{B as t,C as e,D as o,E as n,g as s,s as i,a,G as r,A as c,H as l,b as u,e as d,h,w as p,i as f,o as m,j as g,l as _,q as v,F as y,m as S,t as k,k as b,u as w,v as x,S as E,x as C,p as L,J as O}from"./index-BnjgV7rC.js";import{r as T}from"./request.CNAVEKUC.js";import{c as I}from"./index.B6QF5Ba_.js";import{t as j}from"./i18n.B-400vmE.js";import{_ as N}from"./_plugin-vue_export-helper.BCo6x5W8.js";function M(){return"undefined"!=typeof navigator&&"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}}const R="function"==typeof Proxy;class U{constructor(t,e){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=e;const o={};if(t.settings)for(const a in t.settings){const e=t.settings[a];o[a]=e.defaultValue}const n=`__vue-devtools-plugin-settings__${t.id}`;let s={...o};try{const t=localStorage.getItem(n),e=JSON.parse(t);Object.assign(s,e)}catch(i){}this.fallbacks={getSettings:()=>s,setSettings(t){try{localStorage.setItem(n,JSON.stringify(t))}catch(i){}s=t}},e.on("plugin:settings:set",((t,e)=>{t===this.plugin.id&&this.fallbacks.setSettings(e)})),this.proxiedOn=new Proxy({},{get:(t,e)=>this.target?this.target.on[e]:(...t)=>{this.onQueue.push({method:e,args:t})}}),this.proxiedTarget=new Proxy({},{get:(t,e)=>this.target?this.target[e]:"on"===e?this.proxiedOn:Object.keys(this.fallbacks).includes(e)?(...t)=>(this.targetQueue.push({method:e,args:t,resolve:()=>{}}),this.fallbacks[e](...t)):(...t)=>new Promise((o=>{this.targetQueue.push({method:e,args:t,resolve:o})}))})}async setRealTarget(t){this.target=t;for(const e of this.onQueue)this.target.on[e.method](...e.args);for(const e of this.targetQueue)e.resolve(await this.target[e.method](...e.args))}}function P(t,e){const o=M(),n=M().__VUE_DEVTOOLS_GLOBAL_HOOK__,s=R&&t.enableEarlyProxy;if(!n||!o.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&s){const i=s?new U(t,n):null;(o.__VUE_DEVTOOLS_PLUGINS__=o.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:t,setupFn:e,proxy:i}),i&&e(i.proxiedTarget)}else n.emit("devtools-plugin:setup",t,e)}
/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */function A(t,e){Object.keys(t).forEach((function(o){return e(t[o],o)}))}function G(t,e,o){return e.indexOf(t)<0&&(o&&o.prepend?e.unshift(t):e.push(t)),function(){var o=e.indexOf(t);o>-1&&e.splice(o,1)}}function D(t,e){t._actions=Object.create(null),t._mutations=Object.create(null),t._wrappedGetters=Object.create(null),t._modulesNamespaceMap=Object.create(null);var o=t.state;H(t,o,[],t._modules.root,!0),F(t,o,e)}function F(s,i,a){var r=s._state,c=s._scope;s.getters={},s._makeLocalGettersCache=Object.create(null);var l=s._wrappedGetters,u={},d={},h=e(!0);h.run((function(){A(l,(function(t,e){u[e]=function(t,e){return function(){return t(e)}}(t,s),d[e]=o((function(){return u[e]()})),Object.defineProperty(s.getters,e,{get:function(){return d[e].value},enumerable:!0})}))})),s._state=n({data:i}),s._scope=h,s.strict&&function(e){t((function(){return e._state.data}),(function(){}),{deep:!0,flush:"sync"})}(s),r&&a&&s._withCommit((function(){r.data=null})),c&&c.stop()}function H(t,e,o,n,s){var i=!o.length,a=t._modules.getNamespace(o);if(n.namespaced&&(t._modulesNamespaceMap[a],t._modulesNamespaceMap[a]=n),!i&&!s){var r=V(e,o.slice(0,-1)),c=o[o.length-1];t._withCommit((function(){r[c]=n.state}))}var l=n.context=function(t,e,o){var n=""===e,s={dispatch:n?t.dispatch:function(o,n,s){var i=B(o,n,s),a=i.payload,r=i.options,c=i.type;return r&&r.root||(c=e+c),t.dispatch(c,a)},commit:n?t.commit:function(o,n,s){var i=B(o,n,s),a=i.payload,r=i.options,c=i.type;r&&r.root||(c=e+c),t.commit(c,a,r)}};return Object.defineProperties(s,{getters:{get:n?function(){return t.getters}:function(){return Q(t,e)}},state:{get:function(){return V(t.state,o)}}}),s}(t,a,o);n.forEachMutation((function(e,o){!function(t,e,o,n){(t._mutations[e]||(t._mutations[e]=[])).push((function(e){o.call(t,n.state,e)}))}(t,a+o,e,l)})),n.forEachAction((function(e,o){var n=e.root?o:a+o,s=e.handler||e;!function(t,e,o,n){(t._actions[e]||(t._actions[e]=[])).push((function(e){var s,i=o.call(t,{dispatch:n.dispatch,commit:n.commit,getters:n.getters,state:n.state,rootGetters:t.getters,rootState:t.state},e);return(s=i)&&"function"==typeof s.then||(i=Promise.resolve(i)),t._devtoolHook?i.catch((function(e){throw t._devtoolHook.emit("vuex:error",e),e})):i}))}(t,n,s,l)})),n.forEachGetter((function(e,o){!function(t,e,o,n){if(t._wrappedGetters[e])return;t._wrappedGetters[e]=function(t){return o(n.state,n.getters,t.state,t.getters)}}(t,a+o,e,l)})),n.forEachChild((function(n,i){H(t,e,o.concat(i),n,s)}))}function Q(t,e){if(!t._makeLocalGettersCache[e]){var o={},n=e.length;Object.keys(t.getters).forEach((function(s){if(s.slice(0,n)===e){var i=s.slice(n);Object.defineProperty(o,i,{get:function(){return t.getters[s]},enumerable:!0})}})),t._makeLocalGettersCache[e]=o}return t._makeLocalGettersCache[e]}function V(t,e){return e.reduce((function(t,e){return t[e]}),t)}function B(t,e,o){var n;return null!==(n=t)&&"object"==typeof n&&t.type&&(o=e,e=t,t=t.type),{type:t,payload:e,options:o}}var $=0;function q(t,e){P({id:"org.vuejs.vuex",app:t,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:["vuex bindings"]},(function(o){o.addTimelineLayer({id:"vuex:mutations",label:"Vuex Mutations",color:J}),o.addTimelineLayer({id:"vuex:actions",label:"Vuex Actions",color:J}),o.addInspector({id:"vuex",label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),o.on.getInspectorTree((function(o){if(o.app===t&&"vuex"===o.inspectorId)if(o.filter){var n=[];X(n,e._modules.root,o.filter,""),o.rootNodes=n}else o.rootNodes=[z(e._modules.root,"")]})),o.on.getInspectorState((function(o){if(o.app===t&&"vuex"===o.inspectorId){var n=o.nodeId;Q(e,n),o.state=function(t,e,o){e="root"===o?e:e[o];var n=Object.keys(e),s={state:Object.keys(t.state).map((function(e){return{key:e,editable:!0,value:t.state[e]}}))};if(n.length){var i=function(t){var e={};return Object.keys(t).forEach((function(o){var n=o.split("/");if(n.length>1){var s=e,i=n.pop();n.forEach((function(t){s[t]||(s[t]={_custom:{value:{},display:t,tooltip:"Module",abstract:!0}}),s=s[t]._custom.value})),s[i]=Y((function(){return t[o]}))}else e[o]=Y((function(){return t[o]}))})),e}(e);s.getters=Object.keys(i).map((function(t){return{key:t.endsWith("/")?K(t):t,editable:!1,value:Y((function(){return i[t]}))}}))}return s}((s=e._modules,(a=(i=n).split("/").filter((function(t){return t}))).reduce((function(t,e,o){var n=t[e];if(!n)throw new Error('Missing module "'+e+'" for path "'+i+'".');return o===a.length-1?n:n._children}),"root"===i?s:s.root._children)),"root"===n?e.getters:e._makeLocalGettersCache,n)}var s,i,a})),o.on.editInspectorState((function(o){if(o.app===t&&"vuex"===o.inspectorId){var n=o.nodeId,s=o.path;"root"!==n&&(s=n.split("/").filter(Boolean).concat(s)),e._withCommit((function(){o.set(e._state.data,s,o.state.value)}))}})),e.subscribe((function(t,e){var n={};t.payload&&(n.payload=t.payload),n.state=e,o.notifyComponentUpdate(),o.sendInspectorTree("vuex"),o.sendInspectorState("vuex"),o.addTimelineEvent({layerId:"vuex:mutations",event:{time:Date.now(),title:t.type,data:n}})})),e.subscribeAction({before:function(t,e){var n={};t.payload&&(n.payload=t.payload),t._id=$++,t._time=Date.now(),n.state=e,o.addTimelineEvent({layerId:"vuex:actions",event:{time:t._time,title:t.type,groupId:t._id,subtitle:"start",data:n}})},after:function(t,e){var n={},s=Date.now()-t._time;n.duration={_custom:{type:"duration",display:s+"ms",tooltip:"Action duration",value:s}},t.payload&&(n.payload=t.payload),n.state=e,o.addTimelineEvent({layerId:"vuex:actions",event:{time:Date.now(),title:t.type,groupId:t._id,subtitle:"end",data:n}})}})}))}var J=8702998,W={label:"namespaced",textColor:16777215,backgroundColor:6710886};function K(t){return t&&"root"!==t?t.split("/").slice(-2,-1)[0]:"Root"}function z(t,e){return{id:e||"root",label:K(e),tags:t.namespaced?[W]:[],children:Object.keys(t._children).map((function(o){return z(t._children[o],e+o+"/")}))}}function X(t,e,o,n){n.includes(o)&&t.push({id:n||"root",label:n.endsWith("/")?n.slice(0,n.length-1):n||"Root",tags:e.namespaced?[W]:[]}),Object.keys(e._children).forEach((function(s){X(t,e._children[s],o,n+s+"/")}))}function Y(t){try{return t()}catch(e){return e}}var Z=function(t,e){this.runtime=e,this._children=Object.create(null),this._rawModule=t;var o=t.state;this.state=("function"==typeof o?o():o)||{}},tt={namespaced:{configurable:!0}};tt.namespaced.get=function(){return!!this._rawModule.namespaced},Z.prototype.addChild=function(t,e){this._children[t]=e},Z.prototype.removeChild=function(t){delete this._children[t]},Z.prototype.getChild=function(t){return this._children[t]},Z.prototype.hasChild=function(t){return t in this._children},Z.prototype.update=function(t){this._rawModule.namespaced=t.namespaced,t.actions&&(this._rawModule.actions=t.actions),t.mutations&&(this._rawModule.mutations=t.mutations),t.getters&&(this._rawModule.getters=t.getters)},Z.prototype.forEachChild=function(t){A(this._children,t)},Z.prototype.forEachGetter=function(t){this._rawModule.getters&&A(this._rawModule.getters,t)},Z.prototype.forEachAction=function(t){this._rawModule.actions&&A(this._rawModule.actions,t)},Z.prototype.forEachMutation=function(t){this._rawModule.mutations&&A(this._rawModule.mutations,t)},Object.defineProperties(Z.prototype,tt);var et=function(t){this.register([],t,!1)};function ot(t,e,o){if(e.update(o),o.modules)for(var n in o.modules){if(!e.getChild(n))return;ot(t.concat(n),e.getChild(n),o.modules[n])}}et.prototype.get=function(t){return t.reduce((function(t,e){return t.getChild(e)}),this.root)},et.prototype.getNamespace=function(t){var e=this.root;return t.reduce((function(t,o){return t+((e=e.getChild(o)).namespaced?o+"/":"")}),"")},et.prototype.update=function(t){ot([],this.root,t)},et.prototype.register=function(t,e,o){var n=this;void 0===o&&(o=!0);var s=new Z(e,o);0===t.length?this.root=s:this.get(t.slice(0,-1)).addChild(t[t.length-1],s);e.modules&&A(e.modules,(function(e,s){n.register(t.concat(s),e,o)}))},et.prototype.unregister=function(t){var e=this.get(t.slice(0,-1)),o=t[t.length-1],n=e.getChild(o);n&&n.runtime&&e.removeChild(o)},et.prototype.isRegistered=function(t){var e=this.get(t.slice(0,-1)),o=t[t.length-1];return!!e&&e.hasChild(o)};var nt=function(t){var e=this;void 0===t&&(t={});var o=t.plugins;void 0===o&&(o=[]);var n=t.strict;void 0===n&&(n=!1);var s=t.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new et(t),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=s;var i=this,a=this.dispatch,r=this.commit;this.dispatch=function(t,e){return a.call(i,t,e)},this.commit=function(t,e,o){return r.call(i,t,e,o)},this.strict=n;var c=this._modules.root.state;H(this,c,[],this._modules.root),F(this,c),o.forEach((function(t){return t(e)}))},st={state:{configurable:!0}};nt.prototype.install=function(t,e){t.provide(e||"store",this),t.config.globalProperties.$store=this,void 0!==this._devtools&&this._devtools&&q(t,this)},st.state.get=function(){return this._state.data},st.state.set=function(t){},nt.prototype.commit=function(t,e,o){var n=this,s=B(t,e,o),i=s.type,a=s.payload,r={type:i,payload:a},c=this._mutations[i];c&&(this._withCommit((function(){c.forEach((function(t){t(a)}))})),this._subscribers.slice().forEach((function(t){return t(r,n.state)})))},nt.prototype.dispatch=function(t,e){var o=this,n=B(t,e),s=n.type,i=n.payload,a={type:s,payload:i},r=this._actions[s];if(r){try{this._actionSubscribers.slice().filter((function(t){return t.before})).forEach((function(t){return t.before(a,o.state)}))}catch(l){}var c=r.length>1?Promise.all(r.map((function(t){return t(i)}))):r[0](i);return new Promise((function(t,e){c.then((function(e){try{o._actionSubscribers.filter((function(t){return t.after})).forEach((function(t){return t.after(a,o.state)}))}catch(l){}t(e)}),(function(t){try{o._actionSubscribers.filter((function(t){return t.error})).forEach((function(e){return e.error(a,o.state,t)}))}catch(l){}e(t)}))}))}},nt.prototype.subscribe=function(t,e){return G(t,this._subscribers,e)},nt.prototype.subscribeAction=function(t,e){return G("function"==typeof t?{before:t}:t,this._actionSubscribers,e)},nt.prototype.watch=function(e,o,n){var s=this;return t((function(){return e(s.state,s.getters)}),o,Object.assign({},n))},nt.prototype.replaceState=function(t){var e=this;this._withCommit((function(){e._state.data=t}))},nt.prototype.registerModule=function(t,e,o){void 0===o&&(o={}),"string"==typeof t&&(t=[t]),this._modules.register(t,e),H(this,this.state,t,this._modules.get(t),o.preserveState),F(this,this.state)},nt.prototype.unregisterModule=function(t){var e=this;"string"==typeof t&&(t=[t]),this._modules.unregister(t),this._withCommit((function(){delete V(e.state,t.slice(0,-1))[t[t.length-1]]})),D(this)},nt.prototype.hasModule=function(t){return"string"==typeof t&&(t=[t]),this._modules.isRegistered(t)},nt.prototype.hotUpdate=function(t){this._modules.update(t),D(this,!0)},nt.prototype._withCommit=function(t){var e=this._committing;this._committing=!0,t(),this._committing=e},Object.defineProperties(nt.prototype,st);const it=N({mixins:[{onShow(){this.checkTokenStatus()},onLoad(){this.checkTokenStatus()},methods:{async checkTokenStatus(){const t=s("token");if(!t)return this.handleNoToken(),!1;try{const e=JSON.parse(atob(t.split(".")[1]));return!(1e3*e.exp<Date.now())||(this.handleTokenExpired(),!1)}catch(e){return console.error("Token解析失败:",e),this.handleInvalidToken(),!1}},handleNoToken(){this.clearLoginInfo(),this.redirectToLogin()},handleTokenExpired(){i({title:"登录已过期，请重新登录",icon:"none"}),this.clearLoginInfo(),this.redirectToLogin()},handleInvalidToken(){i({title:"登录状态异常，请重新登录",icon:"none"}),this.clearLoginInfo(),this.redirectToLogin()},clearLoginInfo(){a("token"),a("userInfo")},redirectToLogin(){const t=r();"/pages/login/index"!==`/${t[t.length-1].route}`&&c({url:"/pages/login/index"})}}}],data:()=>({statusBarHeight:20,baseURL:I.apiBaseUrl,bannerList:[],notice:null,trendList:[],eventSource:null,sseCheckTimer:null,sseReconnectAttempts:0,maxReconnectAttempts:5,isLoading:!0,skeletonCount:8,serviceMenu:[{icon:"/static/tools/platform.png",text:"平台介绍"},{icon:"/static/tools/notice.png",text:"官方公告"},{icon:"/static/tools/kefu.png",text:"官方客服"},{icon:"/static/tools/setting.png",text:"设置"}],showQRCode:!1}),created(){const t=l();this.statusBarHeight=t.statusBarHeight},onShow(){this.loadUserInfo(),this.checkTokenStatus(),this.loadData(),"undefined"!=typeof window&&window.EventSource&&(this.sseReconnectAttempts=0,setTimeout((()=>{this.checkSSEConnection()}),500),this.startSSEHealthCheck())},onLoad(){this.checkTokenStatus(),this.loadData(),"undefined"!=typeof window&&window.EventSource&&(this.initSSE(),this.startSSEHealthCheck())},onHide(){this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.stopSSEHealthCheck()},onUnload(){this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.stopSSEHealthCheck()},methods:{t:j,async loadUserInfo(){try{const t=await T({url:"/api/user/info",method:"GET"});if(200===t.code&&t.data&&(u("userInfo",t.data),!t.data.status))return void c({url:"/pages/login/index"})}catch(t){console.error("获取用户信息失败:",t),i({title:j("fetchLatestDataFail"),icon:"none"})}},handleBannerClick(t){t.link&&d({url:t.link})},async loadData(){if(await this.checkTokenStatus())try{await Promise.all([this.loadBannerList(),this.loadNotice()])}catch(t){console.error("加载数据失败:",t),"请先登录"===t.message||401===t.statusCode?(this.clearLoginInfo(),this.redirectToLogin()):i({title:j("loadDataFail"),icon:"none"})}},async loadBannerList(){try{const t=await T({url:"/api/banner/list",method:"GET"});200===t.code&&t.data&&t.data.length>0&&(this.bannerList=t.data.map((t=>({id:t.id,imageUrl:t.imageUrl,title:t.title}))))}catch(t){console.error("获取轮播图失败:",t)}},async loadNotice(){try{const t=await T({url:"/api/notice/active",method:"GET"});200===t.code&&(this.notice=t.data)}catch(t){throw console.error("获取公告失败:",t),t}},getImageUrl(t){return t?t.startsWith("/static/")?t:t.startsWith("/")?`${this.baseURL}${t}`:t:""},initSSE(){this.eventSource&&(this.eventSource.close(),this.eventSource=null);const t=s("token")||"";if(t)try{console.log("正在初始化SSE连接..."),this.eventSource=new window.EventSource(this.baseURL+"/api/market/trend/stream?token="+encodeURIComponent(t)),this.eventSource.onopen=()=>{console.log("SSE连接已建立"),this.sseReconnectAttempts=0},this.eventSource.onmessage=t=>{let e=t.data;e.startsWith("data: ")&&(e=e.slice(6));try{const t=JSON.parse(e);t.forEach((t=>{t.cnyPrice=(7*Number(t.price)).toFixed(2)})),this.trendList=t,this.isLoading=!1,console.log("SSE数据更新成功，数据条数:",t.length)}catch(o){console.error("SSE数据解析失败",o),this.isLoading=!1}},this.eventSource.onerror=t=>{if(console.error("SSE连接异常",t),this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.isLoading=!1,this.sseReconnectAttempts<this.maxReconnectAttempts){this.sseReconnectAttempts++;const t=Math.min(3e3*this.sseReconnectAttempts,3e4);console.log(`SSE重连第${this.sseReconnectAttempts}次，${t/1e3}秒后重试...`),setTimeout((()=>{"undefined"!=typeof window&&window.EventSource&&!this.eventSource&&this.initSSE()}),t)}else console.log("SSE重连次数已达上限，停止重连")}}catch(e){console.error("SSE初始化失败:",e),this.isLoading=!1}else console.log("SSE初始化失败：缺少token")},checkSSEConnection(){this.eventSource&&this.eventSource.readyState!==EventSource.CLOSED?this.eventSource.readyState===EventSource.CONNECTING?console.log("SSE正在连接中..."):this.eventSource.readyState===EventSource.OPEN&&console.log("SSE连接正常"):this.sseReconnectAttempts<this.maxReconnectAttempts?(console.log("SSE连接已断开，尝试重新连接..."),"undefined"!=typeof window&&window.EventSource&&this.initSSE()):console.log("SSE重连次数已达上限，跳过重连")},startSSEHealthCheck(){this.stopSSEHealthCheck(),this.sseCheckTimer=setInterval((()=>{this.checkSSEConnection()}),3e4)},stopSSEHealthCheck(){this.sseCheckTimer&&(clearInterval(this.sseCheckTimer),this.sseCheckTimer=null)},goToKline(t){d({url:`/pages/kline/index?symbol=${encodeURIComponent(t.pairName)}&from=home`})},handleMenuClick(t){"官方客服"===t.text?this.showQRCode=!0:"平台介绍"===t.text?d({url:"/pages/legalsystem/index"}):"设置"===t.text?d({url:"/pages/accountset/index"}):"官方公告"===t.text&&d({url:"/pages/noticelist/index"})},closeQRCode(){this.showQRCode=!1}}},[["render",function(t,e,o,n,s,i){const a=x,r=O,c=E,l=C,u=f;return m(),h(u,{class:"container"},{default:p((()=>[g(c,{class:"swiper",circular:"","indicator-dots":!0,autoplay:!0,interval:5e3,duration:2e3,"previous-margin":"20rpx","next-margin":"20rpx","display-multiple-items":1,"easing-function":"easeInOutCubic"},{default:p((()=>[(m(!0),_(y,null,v(s.bannerList,((t,e)=>(m(),h(r,{key:e,class:"swiper-item"},{default:p((()=>[g(a,{src:i.getImageUrl(t.imageUrl),mode:"aspectFill",class:"swiper-image",onClick:e=>i.handleBannerClick(t)},null,8,["src","onClick"])])),_:2},1024)))),128))])),_:1}),s.notice?(m(),h(u,{key:0,class:"notice-bar"},{default:p((()=>[g(u,{class:"notice-left"},{default:p((()=>[g(l,{class:"notice-label"},{default:p((()=>[S(k(i.t("notice")),1)])),_:1})])),_:1}),g(u,{class:"notice-right"},{default:p((()=>[g(u,{class:"notice-content"},{default:p((()=>[g(u,{class:"marquee-text"},{default:p((()=>[S(k(s.notice.content+"                    "+s.notice.content),1)])),_:1})])),_:1})])),_:1})])),_:1})):b("",!0),g(u,{class:"section-card glass-effect animate-item"},{default:p((()=>[g(u,{class:"grid-menu"},{default:p((()=>[(m(!0),_(y,null,v(s.serviceMenu,((t,e)=>(m(),h(u,{class:"menu-item",key:e,onClick:e=>i.handleMenuClick(t)},{default:p((()=>[g(a,{src:t.icon,class:"menu-icon",mode:"aspectFit"},null,8,["src"]),g(l,{class:"menu-text"},{default:p((()=>[S(k(t.text),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1}),s.showQRCode?(m(),h(u,{key:1,class:"qrcode-popup",onClick:i.closeQRCode},{default:p((()=>[g(u,{class:"popup-content",onClick:e[0]||(e[0]=w((()=>{}),["stop"]))},{default:p((()=>[g(u,{class:"close-btn",onClick:i.closeQRCode},{default:p((()=>[S("×")])),_:1},8,["onClick"]),g(a,{class:"qrcode-image",src:"/assets/qrcode-DyUjIpk2.png",mode:"aspectFit"}),g(l,{class:"popup-text"},{default:p((()=>[S("扫一扫上面的二维码，加我为好友")])),_:1})])),_:1})])),_:1},8,["onClick"])):b("",!0),g(u,{class:"market-trend-section"},{default:p((()=>[g(u,{class:"market-trend-card"},{default:p((()=>[g(u,{class:"market-trend-header"},{default:p((()=>[g(l,{class:"market-trend-title"},{default:p((()=>[S("热门合约")])),_:1})])),_:1}),g(u,{class:"market-trend-content"},{default:p((()=>[s.isLoading?(m(),h(u,{key:0},{default:p((()=>[(m(!0),_(y,null,v(s.skeletonCount,(t=>(m(),h(u,{key:t,class:"trend-item"},{default:p((()=>[g(u,{class:"coin-logo skeleton-bg"}),g(u,{class:"coin-info"},{default:p((()=>[g(u,{class:"skeleton-text skeleton-title"}),g(u,{class:"skeleton-tag"})])),_:1}),g(u,{class:"coin-price"},{default:p((()=>[g(u,{class:"skeleton-text skeleton-price"}),g(u,{class:"skeleton-text skeleton-cny"})])),_:1})])),_:2},1024)))),128))])),_:1})):(m(),h(u,{key:1},{default:p((()=>[(m(!0),_(y,null,v(s.trendList,(t=>(m(),h(u,{key:t.pairName,class:"trend-item",onClick:e=>i.goToKline(t)},{default:p((()=>[g(a,{src:i.getImageUrl(t.logoUrl),class:"coin-logo",mode:"aspectFit"},null,8,["src"]),g(u,{class:"coin-info"},{default:p((()=>[g(l,{class:"coin-en"},{default:p((()=>[S(k(t.pairName.replace("USDT","")),1)])),_:2},1024)])),_:2},1024),g(u,{class:"coin-price",style:{display:"flex","align-items":"center","justify-content":"space-between"}},{default:p((()=>[g(u,{style:{display:"flex","flex-direction":"column","align-items":"flex-end"}},{default:p((()=>[g(l,{class:"price-main"},{default:p((()=>[S(k(Number(t.price)>=1?Number(t.price).toLocaleString(void 0,{minimumFractionDigits:2,maximumFractionDigits:2}):Number(t.price).toFixed(8).replace(/0+$/,"").replace(/\.$/,"")),1)])),_:2},1024),g(l,{class:"price-cny"},{default:p((()=>[S("¥"+k(t.cnyPrice?t.cnyPrice:"--"),1)])),_:2},1024)])),_:2},1024),g(u,{class:L(["change-tag",Number(t.change)>0?"rise":"fall"])},{default:p((()=>[g(l,{class:"change-icon"},{default:p((()=>[Number(t.change)>0?(m(),h(l,{key:0},{default:p((()=>[S("+")])),_:1})):(m(),h(l,{key:1},{default:p((()=>[S("-")])),_:1}))])),_:2},1024),S(" "+k(Math.abs(Number(t.change)).toFixed(2))+"% ",1)])),_:2},1032,["class"])])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1}))])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-33a448a4"]]);export{it as default};
