<template>
  <view class="container">
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">我的资产</text>
        <view class="right-area">
          <text class="record-link" @click="goToRecord">划转记录</text>
        </view>
      </view>
    </view>
    <view class="asset-header">
      <view class="account-summary">
        <view class="account-block">
          <text class="account-title">资金账户</text>
          <view class="account-row">
            <text>总余额：</text>
            <text class="account-value">{{ Number(fundTotal).toFixed(4) }}USDT</text>
          </view>
          <view class="account-row">
          <!--  <text>可用：</text>
            <text class="account-value">{{ Number(fundAvailable).toFixed(2) }}USDT</text> -->
            <text>冻结：</text>
            <text class="account-value">{{ Number(fundFrozen).toFixed(2) }}</text>
          </view>
		  
        </view>
        <view class="account-block">
          <text class="account-title">跟单账户</text>
          <view class="account-row">
            <text>总余额：</text>
            <text class="account-value">{{ Number(copyTotal).toFixed(2) }}</text>
          </view>
        </view>
      </view>
      <view class="btn-group">
        <button class="asset-btn" @click="onTransfer">互转</button>
        <button class="asset-btn" @click="onWithdraw">提现</button>
      </view>
    </view>
    <view class="transaction-card">
      <view class="section-title-row">
        <text class="section-title">我的交易</text>
      </view>
      <!-- Tab切换 -->
      <view class="tab-container">
        <view class="tab-item" :class="{ active: activeTab === 1 }" @click="switchTab(1)">
          <text class="tab-text">资金账户</text>
        </view>
        <view class="tab-item" :class="{ active: activeTab === 2 }" @click="switchTab(2)">
          <text class="tab-text">跟单账户</text>
        </view>
      </view>
      <!-- 交易列表 -->
      <view class="transaction-list" v-if="currentList.length > 0">
        <view class="transaction-item" v-for="(item, index) in currentList" :key="item.id">
          <view class="transaction-left">
            <text class="transaction-type">{{ item.tradeType }}</text>
            <text class="transaction-time">{{ formatTime(item.createTime) }}</text>
          </view>
          <view class="transaction-right">
            <text class="transaction-amount" :class="{ 'income': item.amount > 0, 'expense': item.amount < 0 }">
              {{ item.amount > 0 ? '+' : '' }}{{ item.amount }}
            </text>
            <!-- <text class="transaction-remark">{{ item.remark }}</text> -->
          </view>
        </view>
        <!-- 加载更多 -->
        <view class="load-more" v-if="hasMore" @click="loadMore">
          <text class="load-more-text">加载更多</text>
        </view>
      </view>
      <!-- 空状态 -->
      <view class="empty-box" v-else>
        <text class="empty-text">暂无交易记录</text>
      </view>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar/custom-navbar.vue'
import request from '@/utils/request'

export default {
  components: { CustomNavbar },
  data() {
    return {
      statusBarHeight: 0,
      fundAvailable: '0',
      fundFrozen: '0',
      fundTotal: '0',
      copyAvailable: '0',
      copyFrozen: '0',
      copyTotal: '0',
      activeTab: 1, // 1: 资金账户, 2: 跟单账户
      fundList: [], // 资金账户交易记录
      copyList: [], // 跟单账户交易记录
      fundPage: 1,
      copyPage: 1,
      fundHasMore: true,
      copyHasMore: true,
      pageSize: 10
    }
  },
  computed: {
    currentList() {
      return this.activeTab === 1 ? this.fundList : this.copyList
    },
    hasMore() {
      return this.activeTab === 1 ? this.fundHasMore : this.copyHasMore
    }
  },
  created() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.getUserInfo()
    this.loadTradeRecords()
  },
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    onTransfer() {
      uni.navigateTo({ url: '/pages/transfer/index' })
    },
    onWithdraw() {
      uni.navigateTo({ url: '/pages/withdraw/index' })
    },
    goToRecord() {
      uni.navigateTo({ url: '/pages/account-transfer/record' })
    },
    async getUserInfo() {
      try {
        const res = await request({
          url: '/api/user/info',
          method: 'GET'
        })
        if (res.code === 200 && res.data) {
          this.fundAvailable = res.data.availableBalance || '0'
          this.fundFrozen = res.data.frozenBalance || '0'
          this.fundTotal = (Number(this.fundAvailable) + Number(this.fundFrozen)).toFixed(2)
          this.copyAvailable = res.data.copyTradeBalance || '0'
          this.copyFrozen = res.data.copyTradeFrozenBalance || '0'
          this.copyTotal = (Number(this.copyAvailable) + Number(this.copyFrozen)).toFixed(2)
        }
      } catch (e) {
        this.fundAvailable = this.fundFrozen = this.fundTotal = '0'
        this.copyAvailable = this.copyFrozen = this.copyTotal = '0'
      }
    },
    switchTab(tab) {
      if (this.activeTab === tab) return
      this.activeTab = tab
      // 如果当前tab没有数据，则加载数据
      if (this.currentList.length === 0) {
        this.loadTradeRecords()
      }
    },
    async loadTradeRecords() {
      const accountType = this.activeTab
      const page = this.activeTab === 1 ? this.fundPage : this.copyPage
      
      try {
        const res = await request({
          url: '/api/trade-record/list',
          method: 'GET',
          params: {
            page: page,
            size: this.pageSize,
            accountType: accountType
          }
        })
        
        if (res.code === 200 && res.data) {
          const newList = res.data.records || []
          const hasMore = res.data.current < res.data.pages
          
          if (this.activeTab === 1) {
            if (page === 1) {
              this.fundList = newList
            } else {
              this.fundList = [...this.fundList, ...newList]
            }
            this.fundHasMore = hasMore
          } else {
            if (page === 1) {
              this.copyList = newList
            } else {
              this.copyList = [...this.copyList, ...newList]
            }
            this.copyHasMore = hasMore
          }
        }
      } catch (e) {
        console.error('加载交易记录失败:', e)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
    loadMore() {
      if (this.activeTab === 1) {
        this.fundPage++
      } else {
        this.copyPage++
      }
      this.loadTradeRecords()
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    formatTime1(timeStr) {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      const now = new Date()
      const diff = now - date
      
      // 小于1分钟
      if (diff < 60000) {
        return '刚刚'
      }
      // 小于1小时
      if (diff < 3600000) {
        return Math.floor(diff / 60000) + '分钟前'
      }
      // 小于24小时
      if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前'
      }
      // 大于24小时，显示具体日期
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hour = date.getHours().toString().padStart(2, '0')
      const minute = date.getMinutes().toString().padStart(2, '0')
      return `${month}-${day} ${hour}:${minute}`
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  background: #121212 !important;
  padding: 100rpx 30rpx 0 30rpx;
  display: flex;
  flex-direction: column;
}
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #121212 !important;
}
.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}
.left-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
}
.right-area {
  min-width: 60rpx;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.page-title {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 30rpx !important;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.record-link {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  margin-right: 0;
  z-index: 10;
  display: inline-block;
  white-space: nowrap;
  padding-left: 12rpx;
  padding-right: 12rpx;
}
.asset-header {
  flex-shrink: 0;
  padding: 40rpx 32rpx 24rpx 32rpx;
  background: #18191D !important;
  border-radius: 24rpx;
  box-sizing: border-box;
  border: none;
  margin-bottom: 24rpx;
}
.balance-row {
  display: flex;
  align-items: flex-end;
  gap: 18rpx;
  margin-bottom: 18rpx;
  .balance-label {
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
  }
  .balance-value {
    color: #fff;
    font-size: 44rpx;
    font-weight: bold;
    margin-left: 18rpx;
  }
}
.asset-group {
  display: flex;
  gap: 40rpx;
  margin-bottom: 24rpx;
  .asset-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .asset-title {
      color: #fff;
      font-size: 26rpx;
      margin-bottom: 8rpx;
    }
    .asset-amount {
      color: #fff;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}
.btn-group {
  display: flex;
  gap: 32rpx;
  margin-bottom: 18rpx;
  .asset-btn {
    flex: 1;
    height: 64rpx;
    background: #fff !important;
    border: none;
    color: #18191D !important;
    font-size: 24rpx;
    font-weight: bold;
    border-radius: 32rpx;
    margin: 0;
    box-shadow: 0 2rpx 8rpx #FFD70044;
    transition: background 0.2s, color 0.2s;
  }
  .asset-btn:active {
    background: #fff;
    color: #111;
  }
}
.section-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  margin-bottom: 12rpx;
  .section-title {
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
  }
  .section-actions {
    display: flex;
    gap: 24rpx;
    .section-action {
      color: #fff;
      font-size: 24rpx;
    }
  }
}
.transaction-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: #18191D !important;
  border-radius: 24rpx;
  box-sizing: border-box;
  border: none;
  padding: 32rpx;
  margin-bottom: 0;
}
.empty-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  .empty-img {
    width: 220rpx;
    height: 220rpx;
    margin-bottom: 24rpx;
    filter: brightness(0.8) sepia(1) hue-rotate(30deg) saturate(4);
  }
  .empty-text {
    color: #fff;
    font-size: 26rpx;
    margin-top: 12rpx;
  }
}
.account-summary {
  background: #18191D !important;
  border-radius: 18rpx;
  margin-bottom: 24rpx;
  padding: 32rpx 24rpx 12rpx 24rpx;
  // box-shadow: 0 2rpx 8rpx #FFD700;
  .account-block {
    margin-bottom: 24rpx;
    &:last-child { margin-bottom: 0; }
    .account-title {
      color: #fff;
      font-size: 28rpx;
      font-weight: 500;
      margin-bottom: 12rpx;
      display: block;
    }
    .account-row {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      color: #fff;
      margin-bottom: 8rpx;
      .account-value {
        color: #fff;
        font-size: 30rpx;
        margin-left: 8rpx;
      }
    }
  }
}
// Tab切换样式
.tab-container {
  display: flex;
  background: #222222;
  border-radius: 16rpx;
  padding: 4rpx;
  margin-bottom: 24rpx;
  .tab-item {
    flex: 1;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    transition: all 0.3s ease;
    .tab-text {
      color: #fff;
      font-size: 26rpx;
      font-weight: 500;
      transition: color 0.3s ease;
    }
    &.active {
      background: #fff !important;
      .tab-text {
        color: #18191D !important;
      }
    }
  }
}
// 交易列表样式
.transaction-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  max-height: 100%;
  .transaction-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
    border-bottom: 1px solid #333333;
    &:last-child {
      border-bottom: none;
    }
    .transaction-left {
      flex: 1;
      display: flex;
      flex-direction: column;
      .transaction-type {
        color: #fff;
        font-size: 24rpx;
        font-weight: 500;
        margin-bottom: 8rpx;
      }
      .transaction-time {
        color: #fff;
        font-size: 24rpx;
      }
    }
    .transaction-right {
      padding-right: 20rpx;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .transaction-amount {
        font-size: 24rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
        &.income {
          color: #02BF87 !important;
        }
        &.expense {
          color: #F34A69 !important;
        }
      }
      .transaction-remark {
        color: #fff;
        font-size: 24rpx;
        max-width: 200rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
// 加载更多样式
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  .load-more-text {
    color: #fff;
    font-size: 24rpx;
    font-weight: 500;
  }
}
</style> 