<template>
  <view class="slider-verify" @touchmove.stop>
    <view class="verify-box">
      <view class="title">
        请完成安全验证
        <text class="close" @click="$emit('close')">×</text>
      </view>
      
      <view class="image-box">
        <!-- 背景图 -->
        <image :src="bgImage" class="bg-image" mode="widthFix"></image>
        <!-- 滑块 -->
        <image 
          :src="blockImage" 
          class="block-image"
          :style="{
            transform: `translate3d(${sliderX}px, ${blockY}px, 0)`,
            transition: isReset ? 'transform 0.3s' : ''
          }"
        ></image>
      </view>
      
      <!-- 滑动条 -->
      <view class="slider">
        <view class="slider-bar" :style="{width: sliderX + 'px'}"></view>
        <view 
          class="slider-button"
          :style="{transform: `translateX(${sliderX}px)`}"
          @touchstart.stop.prevent="touchStart"
          @touchmove.stop.prevent="touchMove"
          @touchend.stop.prevent="touchEnd"
        >
          <uni-icons type="arrowright" size="20" color="#666"></uni-icons>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  name: 'SliderVerify',
  props: {
    verifyKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      bgImage: '',
      blockImage: '',
      blockY: 0,
      sliderX: 0,    // 滑块的水平位置
      startX: 0,     // 触摸开始位置
      originX: 0,    // 滑动开始时的位置
      isMoving: false,
      isReset: false,
      maxMoveX: 280,  // 最大可滑动距离
      currentKey: ''  // 添加存储当前验证码的 key
    }
  },
  watch: {
    verifyKey: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.initVerify()
        }
      }
    }
  },
  methods: {
    async initVerify() {
      try {
        const res = await request({
          url: '/api/auth/verify/get',
          method: 'GET'
        })
        this.bgImage = res.data.bgImage
        this.blockImage = res.data.blockImage
        this.blockY = res.data.blockY
        this.currentKey = res.data.key
        this.sliderX = 0
        console.log('Got verify key:', this.currentKey)
      } catch(e) {
        console.error('获取验证图片失败:', e)
        uni.showToast({
          title: '初始化验证失败',
          icon: 'none'
        })
      }
    },
    
    touchStart(e) {
      this.isMoving = true
      this.startX = e.touches[0].pageX
      this.originX = this.sliderX
    },
    
    touchMove(e) {
      if (!this.isMoving) return
      
      const touch = e.touches[0]
      const moveX = touch.pageX - this.startX + this.originX
      
      // 限制滑动范围
      this.sliderX = Math.max(0, Math.min(moveX, this.maxMoveX))
    },
    
    async reset() {
      this.isReset = true
      this.sliderX = 0
      
      await new Promise(resolve => setTimeout(resolve, 300))
      this.isReset = false
      await this.initVerify()
    },
    
    async touchEnd() {
      if (!this.isMoving) return
      this.isMoving = false
      
      try {
        console.log('Sending verify check - Key:', this.currentKey, 'MoveX:', Math.round(this.sliderX))
        const res = await request({
          url: '/api/auth/verify/check',
          method: 'POST',
          data: {
            moveX: Math.round(this.sliderX),
            key: this.currentKey
          }
        })
        
        if (res.data.success) {
          this.$emit('success', res.data.verifyToken)
        } else {
          console.log('Verify failed, resetting...')
          await this.reset()
        }
      } catch(e) {
        console.error('验证失败:', e)
        await this.reset()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.slider-verify {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  
  .verify-box {
    width: 320px;
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    
    .title {
      font-size: 16px;
      color: #333;
      margin-bottom: 15px;
      position: relative;
      
      .close {
        position: absolute;
        right: 0;
        top: 0;
        font-size: 24px;
        color: #999;
        cursor: pointer;
      }
    }
    
    .image-box {
      position: relative;
      width: 100%;
      margin-bottom: 15px;
      overflow: hidden;
      
      .bg-image {
        display: block;
        width: 100%;
        height: auto;
      }
      
      .block-image {
        position: absolute;
        left: 0;  // 初始位置从左侧开始
        top: 0;
        width: 50px;  // 与后端生成的滑块大小一致
        height: 50px;
        cursor: pointer;
        will-change: transform;
        z-index: 2;
      }
    }
    
    .slider {
      position: relative;
      height: 40px;
      background: #f5f5f5;
      border-radius: 20px;
      
      .slider-bar {
        position: absolute;
        height: 100%;
        background: #22d1ee;
        border-radius: 20px;
        transition: width 0.1s;
      }
      
      .slider-button {
        position: absolute;
        top: 0;
        left: 0;
        width: 40px;
        height: 40px;
        background: #fff;
        border-radius: 50%;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        transform: translateX(0);
        transition: transform 0.1s;
        will-change: transform;
        touch-action: none;
        z-index: 2;
      }
    }
  }
}
</style> 