<template>
  <view class="safepwd-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="left-area" @click="handleBack">
          <uni-icons type="left" size="20" color="#fff"></uni-icons>
        </view>
        <text class="page-title">修改安全密码</text>
        <view class="right-area"></view>
      </view>
    </view>
    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 当前手机号 -->
      <view class="phone-section">
        <text class="label">当前绑定邮箱：</text>
        <text class="phone">{{ maskEmail(userInfo.email) }}</text>
      </view>
      <!-- 表单项 -->
      <view class="form-section">
        <view class="form-item">
          <text class="form-label">安全密码</text>
          <input 
            class="form-input" 
            type="password" 
            v-model="password"
            placeholder="请输入安全密码"
            placeholder-style="color: #a18989;"
            maxlength="15"
          />
        </view>
        <view class="form-item">
          <text class="form-label">验证码</text>
          <view class="verify-group">
            <input 
              class="form-input" 
              type="text" 
              v-model="verifyCode"
              placeholder="验证码"
              placeholder-style="color: #a18989;"
            />
            <button 
              class="verify-btn" 
              :disabled="counting > 0"
              @click="sendVerifyCode"
            >
              {{ counting > 0 ? `${counting}s` : '发送验证码' }}
            </button>
          </view>
        </view>
      </view>
      <!-- 提交按钮 -->
      <button class="submit-btn" @click="handleSubmit">确定</button>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      statusBarHeight: 0,
      password: '',
      verifyCode: '',
      counting: 0,
      timer: null,
      userInfo: {}
    }
  },
  onShow() {
    this.getUserInfo()
  },
  created() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    async getUserInfo() {
      try {
        const res = await request({
          url: '/api/user/info',
          method: 'GET'
        })
        if (res.code === 200) {
          this.userInfo = res.data
        }
      } catch (error) {
        console.error('获取用户信息失败', error)
      }
    },
    async sendVerifyCode() {
      if (this.counting > 0) return
      try {
        const res = await request({
          url: '/api/auth/send-reset-code-email',
          method: 'POST',
          params: { email: this.userInfo.email }
        })
        if (res.code === 200) {
          uni.showToast({
            title: '验证码已发送',
            icon: 'none'
          })
          // 开始倒计时
          this.counting = 60
          if (this.timer) clearInterval(this.timer)
          this.timer = setInterval(() => {
            if (this.counting > 0) {
              this.counting--
            } else {
              clearInterval(this.timer)
              this.timer = null
            }
          }, 1000)
        }
      } catch (error) {
        uni.showToast({
          title: error.message || '发送失败',
          icon: 'none'
        })
      }
    },
    async handleSubmit() {
      if (!this.password) {
        uni.showToast({
          title: '请输入安全密码',
          icon: 'none'
        })
        return
      }
      if (!this.verifyCode) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        })
        return
      }
      try {
        uni.showLoading({
          title: '提交中...'
        })
        const res = await request({
          url: '/api/user/security/password/update',
          method: 'POST',
          data: {
            securityPassword: this.password,
            verifyCode: this.verifyCode
          }
        })
        if (res.code === 200) {
          uni.showToast({
            title: '安全密码修改成功',
            icon: 'success'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      } catch (error) {
        uni.showToast({
          title: error.message || '修改失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },
    handleBack() {
      uni.navigateBack()
    },
    maskEmail(email) {
      if (!email) return '';
      const [name, domain] = email.split('@');
      if (!domain) return email;
      if (name.length <= 2) {
        return '*'.repeat(name.length) + '@' + domain;
      }
      return name[0] + '*'.repeat(name.length - 2) + name[name.length - 1] + '@' + domain;
    }
  }
}
</script>

<style lang="scss" scoped>
.safepwd-container {
  min-height: 100vh;
  background: #121212 !important;
  .custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #121212 !important;
    .navbar-content {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;
      .left-area {
        width: 80rpx;
        height: 44px;
        display: flex;
        align-items: center;
        &:active {
          opacity: 0.7;
        }
      }
      .page-title {
        color: #fff;
        font-size: 30rpx;
        font-weight: 500;
      }
      .right-area {
        width: 80rpx;
      }
    }
  }
  .form-content {
    padding: calc(var(--status-bar-height) + 44px) 30rpx 40rpx;
    .phone-section {
      padding: 30rpx 0;
      padding-top: 50rpx;
      display: flex;
      align-items: center;
      .label {
        color: #fff;
        font-size: 28rpx;
      }
      .phone {
        color: #fff;
        font-size: 28rpx;
      }
    }
    .form-section {
      .form-item {
        margin-bottom: 30rpx;
        display: flex;
        align-items: center;
        .form-label {
          width: 140rpx;
          color: #fff;
          font-size: 28rpx;
        }
        .form-input {
          flex: 1;
          height: 56rpx;
          background: #1E1E1E !important;
          border-radius: 8rpx;
          border: none;
          padding: 0 20rpx;
          font-size: 24rpx;
          color: #fff;
        }
        .verify-group {
          flex: 1;
          display: flex;
          gap: 20rpx;
          .form-input {
            flex: 1;
          }
          .verify-btn {
            min-width: 100rpx;
            width: auto;
            height: 56rpx;
            line-height: 56rpx;
            border-radius: 8rpx;
            font-size: 24rpx;
            color: #18191D !important;
            background: #fff !important;
            border: none;
            margin-left: 12rpx;
            margin-right: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            padding: 0 18rpx;
            font-weight: normal;
            letter-spacing: 2rpx;
            transition: background 0.2s, color 0.2s;
            &:active {
              opacity: 0.8;
              background: #eee !important;
              color: #18191D !important;
            }
            &:disabled {
              opacity: 0.6;
              color: rgba(24, 25, 29, 0.5) !important;
              background: #fff !important;
            }
          }
        }
      }
    }
    .submit-btn {
     width: 100%;
     height: 90rpx;
     line-height: 90rpx;
     background: #fff !important;
     border-radius: 8rpx;
     color: #18191D !important;
     font-size: 28rpx;
     margin-top: 60rpx;
     font-weight: 500;
     border: none;
     box-shadow: 0 2rpx 8rpx #FFD70044;
     transition: background 0.2s, color 0.2s;
     &:active {
       opacity: 0.8;
       background: #eee !important;
       color: #18191D !important;
     }
    }
  }
}
</style>
