import{H as t,n as e,e as s,f as a,h as i,w as o,i as l,o as r,j as c,m as n,z as d,l as h,q as p,F as u,x as g,R as f,p as m,t as _,Q as y}from"./index-BnjgV7rC.js";import{_ as T}from"./uni-icons.9b9P_X39.js";import{r as S}from"./uni-app.es.BuhgQoIe.js";import{r as x}from"./request.CNAVEKUC.js";import{_ as H}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const M=H({data:()=>({statusBarHeight:0,scrollHeight:0,page:1,pageSize:10,hasMore:!0,total:0,noticeList:[]}),created(){const e=t();this.statusBarHeight=e.statusBarHeight,this.scrollHeight=e.windowHeight,this.loadNoticeList()},methods:{handleBack(){e()},async loadNoticeList(){try{const t=await x({url:"/api/notice/list",method:"GET",data:{page:this.page,size:this.pageSize}});if(200===t.code&&t.data){const{records:e,total:s,current:a,size:i}=t.data;this.total=s,1===this.page?this.noticeList=e:this.noticeList=[...this.noticeList,...e],this.hasMore=this.noticeList.length<s,e&&0!==e.length||(this.hasMore=!1)}}catch(t){console.error("获取公告列表失败:",t),this.hasMore=!1}},loadMore(){this.hasMore&&(this.page++,this.loadNoticeList())},goToDetail(t){s({url:`/pages/noticedetail/index?id=${t.id}`})},formatTime(t){if(!t)return"";const e=new Date(t);return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")} ${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},getNoticeTypeText(t){switch(t){case 1:return"重要";case 2:default:return"通知";case 3:return"系统";case 4:return"活动";case 5:return"维护"}},parseContent:t=>t?(t=t.replace(/<script[^>]*>[\s\S]*?<\/script>/gi,"")).replace(/<[^>]+>/g,(t=>t.replace(/style="[^"]*"/g,'style="color: #FFD70099;"'))):""}},[["render",function(t,e,s,x,H,M){const k=S(a("uni-icons"),T),L=l,w=g,z=y,j=f;return r(),i(L,{class:"notice-container"},{default:o((()=>[c(L,{class:"custom-navbar",style:d({paddingTop:H.statusBarHeight+"px"})},{default:o((()=>[c(L,{class:"navbar-content"},{default:o((()=>[c(L,{class:"left-area",onClick:M.handleBack},{default:o((()=>[c(k,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),c(w,{class:"page-title"},{default:o((()=>[n("官方公告")])),_:1}),c(L,{class:"right-area"})])),_:1})])),_:1},8,["style"]),c(j,{class:"notice-list","scroll-y":"",onScrolltolower:M.loadMore,style:d({height:H.scrollHeight+"px"})},{default:o((()=>[(r(!0),h(u,null,p(H.noticeList,((t,e)=>(r(),i(L,{class:"notice-item",key:e,onClick:e=>M.goToDetail(t)},{default:o((()=>[c(L,{class:"notice-content"},{default:o((()=>[c(L,{class:"notice-title"},{default:o((()=>[c(w,{class:m(["tag",{important:1===t.noticeType||1===t.isTop}])},{default:o((()=>[n(_(M.getNoticeTypeText(t.noticeType)),1)])),_:2},1032,["class"]),n(" "+_(t.title),1)])),_:2},1024),c(z,{class:"notice-desc",nodes:M.parseContent(t.content)},null,8,["nodes"]),c(L,{class:"notice-footer"},{default:o((()=>[c(w,{class:"notice-time"},{default:o((()=>[n(_(M.formatTime(t.createTime)),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),c(L,{class:"notice-right"},{default:o((()=>[c(k,{type:"right",size:"16",color:"#fff"})])),_:1})])),_:2},1032,["onClick"])))),128)),H.hasMore?(r(),i(L,{key:0,class:"load-more"},{default:o((()=>[c(w,{class:"load-text"},{default:o((()=>[n("加载中...")])),_:1})])),_:1})):(r(),i(L,{key:1,class:"load-more"},{default:o((()=>[c(w,{class:"load-text"},{default:o((()=>[n("没有更多数据了")])),_:1})])),_:1}))])),_:1},8,["onScrolltolower","style"])])),_:1})}],["__scopeId","data-v-8ab03b5b"]]);export{M as default};
