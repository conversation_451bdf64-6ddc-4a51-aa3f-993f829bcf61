import{s,K as a,f as e,h as t,w as o,i as d,o as r,j as c,m as l,t as n,v as i,x as u}from"./index-BnjgV7rC.js";import{C as f}from"./custom-navbar.Bf8ZgRC_.js";import{r as m}from"./uni-app.es.BuhgQoIe.js";import{u as p}from"./uqrcode.BN_KsmDm.js";import{r as _}from"./request.CNAVEKUC.js";import{_ as h}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./uni-icons.9b9P_X39.js";import"./index.B6QF5Ba_.js";const y=h({components:{CustomNavbar:f},data:()=>({address:"",contractAddress:"******************************************",qrCodeUrl:""}),async onLoad(){await this.getWalletAddress()},methods:{async getWalletAddress(){try{const s=await _({url:"/api/wallet/address",method:"GET"});200===s.code&&s.data&&(this.address=s.data.address,await this.generateQrCode())}catch(a){s({title:a.message||"获取钱包地址失败",icon:"none"})}},async generateQrCode(){try{const s=await p.generate(this.address);this.qrCodeUrl=s.tempFilePath}catch(a){this.qrCodeUrl="",s({title:"二维码生成失败",icon:"none"})}},copyText(e){a({data:e,success:()=>{s({title:"复制成功",icon:"none"})}})}}},[["render",function(s,a,p,_,h,y){const C=m(e("custom-navbar"),f),q=i,w=d,x=u;return r(),t(w,{class:"container"},{default:o((()=>[c(C,{title:"充值",showBack:!0}),c(w,{class:"content"},{default:o((()=>[c(w,{class:"qrcode-box"},{default:o((()=>[h.qrCodeUrl?(r(),t(q,{key:0,src:h.qrCodeUrl,class:"qrcode-img",mode:"aspectFit"},null,8,["src"])):(r(),t(w,{key:1,class:"qrcode-placeholder"},{default:o((()=>[l("二维码生成中...")])),_:1}))])),_:1}),c(w,{class:"info-row"},{default:o((()=>[c(w,{class:"info-col"},{default:o((()=>[c(x,{class:"label"},{default:o((()=>[l("主网")])),_:1}),c(x,{class:"value"},{default:o((()=>[l("BNB Smart Chain (BEP20)")])),_:1}),c(x,{class:"contract-info"},{default:o((()=>[l("合约地址："+n(h.contractAddress?"***"+h.contractAddress.slice(-5):""),1)])),_:1})])),_:1})])),_:1}),c(w,{class:"info-row"},{default:o((()=>[c(w,{class:"info-col"},{default:o((()=>[c(x,{class:"label"},{default:o((()=>[l("充值地址")])),_:1}),c(w,{class:"value-row"},{default:o((()=>[c(x,{class:"value address"},{default:o((()=>[l(n(h.address),1)])),_:1}),c(w,{class:"copy-icon",onClick:a[0]||(a[0]=s=>y.copyText(h.address))})])),_:1})])),_:1})])),_:1}),c(w,{class:"desc-card"},{default:o((()=>[c(x,{class:"desc-title"},{default:o((()=>[l("充值说明")])),_:1}),c(x,{class:"desc-text"},{default:o((()=>[l(" 1. 请务必通过链上转账方式充值，确保选择正确的主网（BEP20），并核对充值地址无误。 2. 仅支持USDT资产充值，充值其他币种将无法到账且不可找回。 3. 转账完成后，需等待链上网络确认，到账时间取决于区块链网络状况。 4. 充值过程中如有疑问，请及时联系客服。 ")])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-f7257e23"]]);export{y as default};
