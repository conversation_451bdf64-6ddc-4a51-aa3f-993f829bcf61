import{H as e,c as t,d as a,s as l,g as o,f as s,h as i,w as r,i as c,o as d,l as n,j as u,F as f,q as h,m as p,z as m,t as g,p as _,N as b,k as y,x as P,I as k,y as L,v}from"./index-BnjgV7rC.js";import{_ as S}from"./uni-icons.9b9P_X39.js";import{r as T}from"./uni-app.es.BuhgQoIe.js";import{_ as N}from"./uni-popup.Cg-HJ3Z1.js";import{c as x}from"./index.B6QF5Ba_.js";import{r as w}from"./request.CNAVEKUC.js";import{_ as F}from"./_plugin-vue_export-helper.BCo6x5W8.js";const D=F({data:()=>({statusBarHeight:20,baseURL:x.apiBaseUrl,activeTab:"hold",activePeriod:60,tradeType:"up",mainPrice:"--",cnyPrice:"--",changeRate:"--",eventSource:null,sellList:[],buyList:[],profitList:[],profitPage:1,pageSize:10,profitTotal:0,loading:!1,profitHasMore:!0,holdList:[],holdPage:1,holdTotal:0,holdHasMore:!0,dealList:[],dealPage:1,dealTotal:0,dealHasMore:!0,trendList:[],currentPair:null,eventSourceTrend:null,currentTime:"",copyTradeBalance:0,amount:"",orderLoading:!1,holdProfitSSE:null,orderbookLoading:!0,leverage:50,takeProfit:"",stopLoss:"",tabDataLoaded:{hold:!1,deal:!1,profit:!1},tabLoading:{hold:!1,deal:!1,profit:!1}}),created(){const t=e();this.statusBarHeight=t.statusBarHeight},mounted(){this.loadAllData(),this.setupSSE(),this.initTrendSSE(),this.loadUserInfo(),this.initHoldProfitSSE()},beforeDestroy(){this.holdProfitSSE&&(this.holdProfitSSE.close(),this.holdProfitSSE=null)},methods:{handleBack(){t({url:"/pages/mine/index"})},setTradeType(e){this.tradeType=e},updateTakeProfitStopLoss(){if(!this.mainPrice||"--"===this.mainPrice||isNaN(this.mainPrice))return;const e=Number(this.mainPrice);this.takeProfit&&""!==this.takeProfit||("up"===this.tradeType?this.takeProfit=(e+1e3).toFixed(2):this.takeProfit=(e-1e3).toFixed(2)),this.stopLoss&&""!==this.stopLoss||("up"===this.tradeType?this.stopLoss=(e-500).toFixed(2):this.stopLoss=(e+500).toFixed(2))},calculateDefaultTakeProfitStopLoss(){if(!this.mainPrice||"--"===this.mainPrice||isNaN(this.mainPrice))return{takeProfit:null,stopLoss:null};const e=Number(this.mainPrice);let t,a;return"up"===this.tradeType?(t=e+1e3,a=e-500):(t=e-1e3,a=e+500),{takeProfit:t,stopLoss:a}},calculateMargin(){if(!this.amount||isNaN(this.amount)||!this.leverage||!this.mainPrice||isNaN(this.mainPrice))return"--";return(Number(this.amount)*Number(this.mainPrice)/Number(this.leverage)).toFixed(2)},calculateMarginValue(){if(!this.amount||isNaN(this.amount)||!this.leverage||!this.mainPrice||isNaN(this.mainPrice))return null;return Number(this.amount)*Number(this.mainPrice)/Number(this.leverage)},calculateRealTimeProfit(e){if(void 0!==e.realTimeProfit)return Number(e.realTimeProfit);if(!this.mainPrice||"--"===this.mainPrice||isNaN(this.mainPrice))return Number(e.profit)||0;const t=Number(this.mainPrice),a=Number(e.openPrice),l=Number(e.positionAmount);if(!a||!l)return Number(e.profit)||0;let o=0;return o=1===e.direction?(t-a)*l:(a-t)*l,o},calculateProfitRate(e){if(void 0!==e.realTimeProfitRate)return Number(e.realTimeProfitRate).toFixed(3)+"%";const t=this.calculateRealTimeProfit(e);if(!e.marginAmount||0===e.marginAmount)return"--";return(t/Number(e.marginAmount)*100).toFixed(3)+"%"},formatDealProfitRate(e){if(!e.profit||!e.marginAmount||0===e.marginAmount)return"--";return(Number(e.profit)/Number(e.marginAmount)*100).toFixed(3)+"%"},formatMargin(e){if(!e||0===e)return"--";const t=Number(e);return isNaN(t)?"--":t.toFixed(2)},updateHoldListProfit(){"hold"===this.activeTab&&this.holdList.length>0&&(this.holdList=[...this.holdList])},async closePosition(e){try{if(!(await new Promise((t=>{a({title:"确认平仓",content:`确定要平仓这个${1===e.direction?"买涨":"买跌"}订单吗？\n数量：${e.positionAmount}\n保证金：${this.formatMargin(e.marginAmount)}\n当前盈利：${this.calculateRealTimeProfit(e).toFixed(3)}`,confirmText:"确认平仓",cancelText:"取消",success:e=>{t(e.confirm)}})}))))return;this.$set(e,"closing",!0);const t=await w({url:"/api/copy/order/close",method:"POST",data:{orderId:e.id}});if(200===t.code){l({title:"平仓成功",icon:"success",duration:2e3});const t=this.holdList.findIndex((t=>t.id===e.id));-1!==t&&this.holdList.splice(t,1),await this.loadUserInfo()}else l({title:t.message||t.msg||"平仓失败",icon:"none",duration:3e3})}catch(t){console.error("平仓失败:",t),l({title:t.message||"平仓失败，请重试",icon:"none",duration:3e3})}finally{this.$set(e,"closing",!1)}},getImageUrl(e){return e?e.startsWith("/static/")?e:e.startsWith("/")?`${this.baseURL}${e}`:e:""},formatPrice(e){if(null==e||""===e||isNaN(e))return"--";let t=Number(e);if(0===t)return"0";let a=t.toString();return a.indexOf(".")>-1&&(a=a.replace(/(\.\d*?[1-9])0+$/,"$1").replace(/\.0+$/,"")),a},formatPrice2:e=>null==e||""===e||isNaN(e)?"--":Number(e).toFixed(2),formatAmount(e){if(null==e||""===e||isNaN(e))return"--";let t=Number(e);if(0===t)return"0";if(t>=1e9)return(t/1e9).toFixed(2)+"B";if(t>=1e6)return(t/1e6).toFixed(2)+"M";if(t>=1e3)return(t/1e3).toFixed(2)+"k";let a=t.toString();return a.length>6&&(a=t.toFixed(4)),a},formatTime(e){if(!e)return"";let t;if("string"==typeof e&&e.length>=19)t=new Date(e);else{if(!("number"==typeof e||"string"==typeof e&&/^\d+$/.test(e)))return"--";{let a=Number(e);if(10===e.length)t=new Date(1e3*a);else{if(13!==e.length)return"--";t=new Date(a)}}}const a=e=>e<10?"0"+e:e;return`${a(t.getMonth()+1)}-${a(t.getDate())} ${a(t.getHours())}:${a(t.getMinutes())}:${a(t.getSeconds())}`},formatSymbol:e=>e?e.replace("/USDT","").replace("USDT",""):"--",formatPairPrice(e){if(null==e||""===e||isNaN(e))return"--";const t=Number(e);return t>=1?t.toFixed(2):t.toFixed(5).replace(/0+$/,"").replace(/\.$/,"")},setupSSE(){this.eventSource&&this.eventSource.close();const e=o("token")||"";e&&(this.eventSource=new window.EventSource(this.baseURL+"/api/market/futures/stream?token="+encodeURIComponent(e)+"&symbol="+encodeURIComponent(this.currentPair)),this.eventSource.onmessage=e=>{const t=JSON.parse(e.data);"orderbook"!==t.type&&"futures_update"!==t.type||(this.buyList=t.buyList||[],this.sellList=t.sellList||[],this.orderbookLoading=!1),"ticker"!==t.type&&"futures_update"!==t.type||(this.mainPrice=t.price||"--",this.cnyPrice=t.cnyPrice||"--",this.changeRate=t.changeRate||"--",this.updateHoldListProfit())},this.eventSource.onerror=()=>{this.eventSource.close(),setTimeout((()=>this.setupSSE()),5e3)})},initTrendSSE(){this.eventSourceTrend&&this.eventSourceTrend.close();const e=o("token")||"";e&&(this.eventSourceTrend=new window.EventSource(this.baseURL+"/api/market/trend/stream?token="+encodeURIComponent(e)),this.eventSourceTrend.onmessage=e=>{let t=e.data;t.startsWith("data: ")&&(t=t.slice(6));try{const e=JSON.parse(t);e.forEach((e=>{e.cnyPrice=(7*Number(e.price)).toFixed(2)})),this.trendList=e,!this.currentPair&&e.length>0&&(this.currentPair=e[0],this.switchPair(e[0]))}catch(a){}},this.eventSourceTrend.onerror=()=>{this.eventSourceTrend.close()})},selectPair(e){this.currentPair=e,this.$refs.pairPopup&&this.$refs.pairPopup.close&&this.$refs.pairPopup.close(),this.switchPair(e)},switchPair(e){this.eventSource&&this.eventSource.close();const t=o("token")||"";t&&(this.eventSource=new window.EventSource(this.baseURL+"/api/market/futures/stream?symbol="+encodeURIComponent(e.pairName)+"&token="+encodeURIComponent(t)),this.eventSource.onmessage=e=>{const t=JSON.parse(e.data);"orderbook"!==t.type&&"futures_update"!==t.type||(this.buyList=t.buyList||[],this.sellList=t.sellList||[],this.orderbookLoading=!1),"ticker"!==t.type&&"futures_update"!==t.type||(this.mainPrice=t.price||"--",this.cnyPrice=t.cnyPrice||"--",this.changeRate=t.changeRate||"--",this.updateHoldListProfit())},this.eventSource.onerror=()=>{this.eventSource.close(),setTimeout((()=>this.switchPair(e)),5e3)})},openPairPopup(){this.$refs.pairPopup&&this.$refs.pairPopup.open()},async loadProfitList(e=!1){this.tabLoading.profit=!0;try{const t=await w({url:"/api/copy/order/profit",method:"GET",params:{page:this.profitPage,pageSize:this.pageSize}});if(200===t.code){const a=t.data.list||[];this.profitList=e?this.profitList.concat(a):a,this.profitTotal=t.data.total,this.profitHasMore=t.data.hasMore}}finally{this.tabLoading.profit=!1}},async loadHoldList(e=!1){this.tabLoading.hold=!0;try{const t=await w({url:"/api/copy/order/hold",method:"GET",params:{page:this.holdPage,pageSize:this.pageSize}});if(200===t.code){const a=t.data.list||[];this.holdList=e?this.holdList.concat(a):a,this.holdTotal=t.data.total,this.holdHasMore=t.data.hasMore}}finally{this.tabLoading.hold=!1}},async loadDealList(e=!1){this.tabLoading.deal=!0;try{const t=await w({url:"/api/copy/order/deal",method:"GET",params:{page:this.dealPage,pageSize:this.pageSize}});if(200===t.code){const a=t.data.list||[];this.dealList=e?this.dealList.concat(a):a,this.dealTotal=t.data.total,this.dealHasMore=t.data.hasMore}}finally{this.tabLoading.deal=!1}},async loadUserInfo(){try{const e=await w({url:"/api/user/info",method:"GET"});200===e.code&&e.data&&(this.copyTradeBalance=e.data.copyTradeBalance)}catch(e){}},async onOrderConfirm(){if(this.orderLoading)return;if(!this.amount||isNaN(this.amount))return void l({title:"请输入正确的数量",icon:"none"});if(Number(this.amount)<=0)return void l({title:"数量必须大于0",icon:"none"});if(!this.mainPrice||isNaN(this.mainPrice))return void l({title:"获取价格失败，请稍后重试",icon:"none"});const e=this.calculateMarginValue();if(null===e)return void l({title:"计算保证金失败，请检查输入",icon:"none"});if(e>Number(this.copyTradeBalance))return void l({title:`保证金不足，需要${e.toFixed(2)}USDT，余额${Number(this.copyTradeBalance).toFixed(2)}USDT`,icon:"none",duration:3e3});const t=this.calculateDefaultTakeProfitStopLoss();let a,o;if(a=this.takeProfit&&""!==this.takeProfit&&!isNaN(this.takeProfit)?Number(this.takeProfit):t.takeProfit,o=this.stopLoss&&""!==this.stopLoss&&!isNaN(this.stopLoss)?Number(this.stopLoss):t.stopLoss,!a||!o)return void l({title:"无法获取当前价格，请稍后重试",icon:"none"});const s=Number(this.mainPrice);if("up"===this.tradeType){if(a<=s)return void l({title:"买涨时止盈价格必须大于当前价格",icon:"none"});if(o>=s)return void l({title:"买涨时止损价格必须小于当前价格",icon:"none"})}else{if(a>=s)return void l({title:"买跌时止盈价格必须小于当前价格",icon:"none"});if(o<=s)return void l({title:"买跌时止损价格必须大于当前价格",icon:"none"})}if(this.currentPair&&this.currentPair.pairName){this.orderLoading=!0;try{const e=await w({url:"/api/copy/order/create",method:"POST",data:{symbol:this.currentPair.pairName,direction:"up"===this.tradeType?1:2,leverage:this.leverage,quantity:Number(this.amount),takeProfit:a,stopLoss:o}});200===e.code?(l({title:"下单成功",icon:"success"}),this.amount="",this.takeProfit="",this.stopLoss="",await this.loadUserInfo(),this.holdPage=1,this.loadHoldList(!1)):l({title:e.message||e.msg||"下单失败",icon:"none"})}catch(i){console.error("下单异常:",i),l({title:i.message||"下单异常",icon:"none"})}finally{this.orderLoading=!1}}else l({title:"请选择币对",icon:"none"})},onProfitLoadMore(){!this.tabLoading.profit&&this.profitHasMore&&(this.profitPage++,this.loadProfitList(!0))},onHoldLoadMore(){!this.tabLoading.hold&&this.holdHasMore&&(this.holdPage++,this.loadHoldList(!0))},onDealLoadMore(){!this.tabLoading.deal&&this.dealHasMore&&(this.dealPage++,this.loadDealList(!0))},onTabChange(e){this.activeTab=e,this.tabDataLoaded[e]||("profit"===e?(this.profitPage=1,this.loadProfitList(!1),this.tabDataLoaded.profit=!0):"hold"===e?(this.holdPage=1,this.loadHoldList(!1),this.tabDataLoaded.hold=!0):"deal"===e&&(this.dealPage=1,this.loadDealList(!1),this.tabDataLoaded.deal=!0))},initHoldProfitSSE(){this.holdProfitSSE&&this.holdProfitSSE.close();const e=o("token")||"";if(!e)return void console.log("Leader页面：没有token，无法建立SSE连接");const t=this.baseURL+"/api/copy/order/hold/profit/stream?token="+encodeURIComponent(e);console.log("Leader页面：建立SSE连接:",t),this.holdProfitSSE=new window.EventSource(t),this.holdProfitSSE.onopen=()=>{console.log("Leader页面：SSE连接已建立")},this.holdProfitSSE.onerror=e=>{console.error("Leader页面：SSE连接错误:",e),this.holdProfitSSE.close(),setTimeout((()=>this.initHoldProfitSSE()),5e3)},this.holdProfitSSE.addEventListener("profit-update",(async e=>{console.log("Leader页面：收到SSE原始事件:",e.data);let t=e.data;t.startsWith("data: ")&&(t=t.slice(6));try{const e=JSON.parse(t);console.log("Leader页面：解析后的持仓盈利数据:",e);let a=!1,o=0;e.forEach((e=>{const t=this.holdList.findIndex((t=>t.id===e.orderId));2===e.status?-1!==t&&(this.holdList.splice(t,1),a=!0,o++,console.log(`订单${e.orderId}已平仓，从持仓列表中移除`)):1===e.status&&-1!==t&&(this.holdList[t].realTimeProfit=e.profit,this.holdList[t].realTimeProfitRate=e.profitRate,this.holdList[t].currentPrice=e.currentPrice,this.holdList[t].status=e.status)})),a&&(console.log(`共移除${o}个已平仓订单`),1===o?l({title:"订单已平仓",icon:"success",duration:2e3}):o>1&&l({title:`${o}个订单已平仓`,icon:"success",duration:2e3}),await this.loadUserInfo(),"hold"===this.activeTab&&this.$forceUpdate())}catch(a){console.error("Leader页面：处理实时盈利数据失败:",a)}}))},async loadAllData(){this.loading=!0,"hold"!==this.activeTab||this.tabDataLoaded.hold||(await this.loadHoldList(!1),this.tabDataLoaded.hold=!0),this.loading=!1},onNumberInput(e,t){let a=t.detail.value;a=a.replace(/[^\d.]/g,"").replace(/\.(?=.*\.)/g,""),a.startsWith("00")&&(a=a.replace(/^0+/,"0")),a.indexOf(".")>-1&&(a=a.replace(/(\.\d{0,6}).*$/,"$1")),this[e]=a},refreshCurrentTab(){const e=this.activeTab;"profit"===e?(this.profitPage=1,this.loadProfitList(!1)):"hold"===e?(this.holdPage=1,this.loadHoldList(!1)):"deal"===e&&(this.dealPage=1,this.loadDealList(!1))}}},[["render",function(e,t,a,l,o,x){const w=c,F=T(s("uni-icons"),S),D=P,R=k,C=L,E=v,U=T(s("uni-popup"),N);return d(),i(w,{class:"futures-page"},{default:r((()=>[o.loading?(d(),n(f,{key:0},[u(w,{class:"skeleton-header"}),u(w,{class:"skeleton-main"},{default:r((()=>[u(w,{class:"skeleton-orderbook"}),u(w,{class:"skeleton-trade-panel"},{default:r((()=>[u(w,{class:"skeleton-timer"}),u(w,{class:"skeleton-main-price"}),u(w,{class:"skeleton-btns"}),u(w,{class:"skeleton-input"}),u(w,{class:"skeleton-period-list"}),u(w,{class:"skeleton-confirm"})])),_:1})])),_:1}),u(w,{class:"skeleton-tabs"},{default:r((()=>[(d(),n(f,null,h(3,(e=>u(w,{class:"skeleton-tab",key:e},{default:r((()=>[u(w,{class:"skeleton-tab-header"}),(d(),n(f,null,h(3,(e=>u(w,{class:"skeleton-tab-row",key:e}))),64))])),_:2},1024))),64))])),_:1})],64)):(d(),n(f,{key:1},[u(w,{class:"custom-navbar",style:m({paddingTop:o.statusBarHeight+"px"})},{default:r((()=>[u(w,{class:"navbar-content"},{default:r((()=>[u(w,{class:"left-area",onClick:x.handleBack},{default:r((()=>[u(F,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),u(D,{class:"page-title"},{default:r((()=>[p("带单交易")])),_:1}),u(w,{class:"right-area"})])),_:1})])),_:1},8,["style"]),u(w,{class:"futures-header"},{default:r((()=>[u(w,{style:{display:"flex","align-items":"center",flex:"1"}},{default:r((()=>[u(w,{class:"pair-select",onClick:x.openPairPopup},{default:r((()=>[u(D,{class:"pair"},{default:r((()=>[p(g(o.currentPair?o.currentPair.pairName.replace("USDT",""):"BTC"),1)])),_:1}),u(D,{style:{color:"#fff","font-size":"10px","margin-left":"6rpx","vertical-align":"middle"}},{default:r((()=>[p("▼")])),_:1})])),_:1},8,["onClick"]),u(w,{class:"trend-rate",style:{"margin-left":"12rpx"}},{default:r((()=>[u(D,{class:_(["rate",Number(o.changeRate)<0?"down":"up"])},{default:r((()=>[p(g(Number(o.changeRate)>0&&!String(o.changeRate).startsWith("+")&&!String(o.changeRate).startsWith("-")?"+":"")+g(x.formatPrice(o.changeRate))+"% ",1)])),_:1},8,["class"])])),_:1})])),_:1}),u(w,{class:"header-btns"})])),_:1}),u(w,{class:"futures-main"},{default:r((()=>[u(w,{class:"orderbook"},{default:r((()=>[o.orderbookLoading?(d(),n(f,{key:0},[u(w,{class:"orderbook-header"},{default:r((()=>[u(D,null,{default:r((()=>[p("价格(USDT)")])),_:1}),u(D,null,{default:r((()=>[p("数量("+g(o.currentPair?o.currentPair.pairName.replace("USDT",""):"BTC")+")",1)])),_:1})])),_:1}),u(w,{class:"orderbook-list"},{default:r((()=>[(d(),n(f,null,h(6,(e=>u(w,{class:"orderbook-row skeleton-row",key:"sell-skeleton"+e}))),64)),u(w,{class:"orderbook-divider"}),(d(),n(f,null,h(6,(e=>u(w,{class:"orderbook-row skeleton-row",key:"buy-skeleton"+e}))),64))])),_:1})],64)):(d(),n(f,{key:1},[u(w,{class:"orderbook-header"},{default:r((()=>[u(D,null,{default:r((()=>[p("价格(USDT)")])),_:1}),u(D,null,{default:r((()=>[p("数量("+g(o.currentPair?o.currentPair.pairName.replace("USDT",""):"BTC")+")",1)])),_:1})])),_:1}),u(w,{class:"orderbook-list"},{default:r((()=>[(d(!0),n(f,null,h(o.sellList.slice(0,6).reverse(),((e,t)=>(d(),i(w,{class:"orderbook-row red",key:"sell"+t},{default:r((()=>[u(D,null,{default:r((()=>[p(g(x.formatPrice2(e.price)),1)])),_:2},1024),u(D,null,{default:r((()=>[p(g(x.formatAmount(e.amount)),1)])),_:2},1024)])),_:2},1024)))),128)),u(w,{class:"orderbook-divider"}),(d(!0),n(f,null,h(o.buyList.slice(0,6),((e,t)=>(d(),i(w,{class:"orderbook-row green",key:"buy"+t},{default:r((()=>[u(D,null,{default:r((()=>[p(g(x.formatPrice2(e.price)),1)])),_:2},1024),u(D,null,{default:r((()=>[p(g(x.formatAmount(e.amount)),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})],64))])),_:1}),u(w,{class:"trade-panel"},{default:r((()=>[u(w,{class:"trade-price"},{default:r((()=>[u(D,{class:"main-price",style:m({color:Number(o.changeRate)>0?"#FF4D4F":Number(o.changeRate)<0?"#00FF99":"#fff"})},{default:r((()=>[p(g(x.formatPrice(o.mainPrice)),1)])),_:1},8,["style"]),u(D,{class:"cny",style:m({color:Number(o.changeRate)>0?"#FF4D4F":Number(o.changeRate)<0?"#00FF99":"#FFE066"})},{default:r((()=>[p(" ≈"+g(x.formatPrice(o.cnyPrice))+"CNY ",1)])),_:1},8,["style"])])),_:1}),u(w,{class:"trade-btns"},{default:r((()=>[u(w,{class:_(["trade-type","up"===o.tradeType?"active":""]),onClick:t[0]||(t[0]=e=>x.setTradeType("up"))},{default:r((()=>[p("买入开多")])),_:1},8,["class"]),u(w,{class:_(["trade-type","down"===o.tradeType?"active":""]),onClick:t[1]||(t[1]=e=>x.setTradeType("down"))},{default:r((()=>[p("卖出开空")])),_:1},8,["class"])])),_:1}),u(w,{class:"trade-amount"},{default:r((()=>[u(w,{class:"leverage-switch-group trade-btns",style:{"margin-bottom":"18rpx"}},{default:r((()=>[u(w,{class:_(["trade-type",50===o.leverage?"active":""]),onClick:t[2]||(t[2]=e=>o.leverage=50)},{default:r((()=>[p("X50")])),_:1},8,["class"]),u(w,{class:_(["trade-type",100===o.leverage?"active":""]),onClick:t[3]||(t[3]=e=>o.leverage=100)},{default:r((()=>[p("X100")])),_:1},8,["class"])])),_:1}),u(w,{class:"trade-info"},{default:r((()=>[u(D,null,{default:r((()=>[p("余额 "),b("span",{style:{color:"#fff"}},g(o.copyTradeBalance)+"USDT",1)])),_:1})])),_:1}),u(w,{style:{position:"relative",display:"flex","align-items":"center"}},{default:r((()=>[u(R,{type:"number",class:"amount-input with-suffix",value:o.amount,placeholder:"数量",style:{flex:"1","padding-right":"60rpx"},onInput:t[4]||(t[4]=e=>x.onNumberInput("amount",e)),maxlength:"10"},null,8,["value"]),b("span",{class:"input-suffix"},g(o.currentPair?o.currentPair.pairName.replace(/(USDT|BTC|ETH|BNB|USD|U)$/i,""):"BTC"),1)])),_:1}),u(w,{style:{display:"flex","align-items":"center"}},{default:r((()=>[u(D,{style:{color:"#fff","font-size":"24rpx","margin-right":"6rpx",width:"70rpx","white-space":"nowrap","text-align":"left",height:"56rpx","line-height":"56rpx","margin-top":"16rpx"}},{default:r((()=>[p("止盈：")])),_:1}),u(R,{type:"number",class:"amount-input",value:o.takeProfit,placeholder:"止盈价(可选)",style:{flex:"1"},onInput:t[5]||(t[5]=e=>x.onNumberInput("takeProfit",e)),maxlength:"10"},null,8,["value"])])),_:1}),u(w,{style:{display:"flex","align-items":"center"}},{default:r((()=>[u(D,{style:{color:"#fff","font-size":"24rpx","margin-right":"6rpx",width:"70rpx","white-space":"nowrap","text-align":"left",height:"56rpx","line-height":"56rpx","margin-top":"16rpx"}},{default:r((()=>[p("止损：")])),_:1}),u(R,{type:"number",class:"amount-input",value:o.stopLoss,placeholder:"止损价(可选)",style:{flex:"1"},onInput:t[6]||(t[6]=e=>x.onNumberInput("stopLoss",e)),maxlength:"10"},null,8,["value"])])),_:1}),u(w,{style:{display:"flex","justify-content":"space-between","align-items":"center","margin-top":"16rpx"}},{default:r((()=>[u(D,{style:{color:"#fff","font-size":"22rpx"}},{default:r((()=>[p("保证金")])),_:1}),u(D,{style:{color:"#fff","font-size":"22rpx","font-weight":"bold"}},{default:r((()=>[p(g(x.calculateMargin()),1)])),_:1})])),_:1})])),_:1}),u(C,{class:"trade-confirm",onClick:x.onOrderConfirm,disabled:o.orderLoading},{default:r((()=>[o.orderLoading?(d(),i(D,{key:0},{default:r((()=>[p("下单中...")])),_:1})):(d(),i(D,{key:1},{default:r((()=>[p("确定")])),_:1}))])),_:1},8,["onClick","disabled"])])),_:1})])),_:1}),u(w,{class:"futures-tabs"},{default:r((()=>[u(w,{class:"tab-list"},{default:r((()=>[u(w,{class:_(["tab","hold"===o.activeTab?"active":""]),onClick:t[7]||(t[7]=e=>x.onTabChange("hold"))},{default:r((()=>[p("持仓")])),_:1},8,["class"]),u(w,{class:_(["tab","deal"===o.activeTab?"active":""]),onClick:t[8]||(t[8]=e=>x.onTabChange("deal"))},{default:r((()=>[p("成交")])),_:1},8,["class"]),u(w,{class:_(["tab","profit"===o.activeTab?"active":""]),onClick:t[9]||(t[9]=e=>x.onTabChange("profit"))},{default:r((()=>[p("盈利")])),_:1},8,["class"])])),_:1}),"hold"===o.activeTab?(d(),i(w,{key:0,class:"hold-card-list",style:{"min-height":"700rpx"}},{default:r((()=>[o.tabLoading.hold&&0===o.holdList.length?(d(),i(w,{key:0,class:"tab-loading"},{default:r((()=>[u(D,{class:"loading-text"},{default:r((()=>[p("加载中...")])),_:1})])),_:1})):y("",!0),0!==o.holdList.length||o.tabLoading.hold?y("",!0):(d(),i(w,{key:1,class:"empty-state"},{default:r((()=>[u(D,{class:"empty-text"},{default:r((()=>[p("暂无持仓数据")])),_:1})])),_:1})),(d(!0),n(f,null,h(o.holdList,((e,t)=>(d(),i(w,{key:e.id,class:"hold-card"},{default:r((()=>[u(w,{class:"hold-card-header"},{default:r((()=>[u(w,{style:{display:"flex","align-items":"center",gap:"8rpx"}},{default:r((()=>[u(D,{class:"hold-symbol"},{default:r((()=>[p(g(x.formatSymbol(e.symbol)),1)])),_:2},1024),u(D,{class:"hold-leverage"},{default:r((()=>[p("x"+g(e.lever||"--"),1)])),_:2},1024),u(D,{class:"hold-direction",style:m(1===e.direction?"background: #02BF87; color: #fff":"background: #F34A69; color: #fff")},{default:r((()=>[p(g(1===e.direction?"买入开多":"卖出开空"),1)])),_:2},1032,["style"])])),_:2},1024),1===e.status?(d(),i(C,{key:0,class:"close-position-btn-header",onClick:t=>x.closePosition(e),disabled:e.closing,style:{"margin-right":"12rpx"}},{default:r((()=>[p(g(e.closing?"平仓中...":"一键平仓"),1)])),_:2},1032,["onClick","disabled"])):y("",!0)])),_:2},1024),u(w,{class:"hold-row-3col"},{default:r((()=>[u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("数量")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(e.positionAmount),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("保证金")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(x.formatMargin(e.marginAmount)),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("开仓价")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(e.openPrice),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-row-3col"},{default:r((()=>[u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("盈利")])),_:1}),u(D,{class:_(["hold-value-compact",x.calculateRealTimeProfit(e)>0?"green":"red"])},{default:r((()=>[p(g(x.calculateRealTimeProfit(e).toFixed(3)),1)])),_:2},1032,["class"])])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("收益率")])),_:1}),u(D,{class:_(["hold-value-compact",x.calculateRealTimeProfit(e)>0?"green":"red"])},{default:r((()=>[p(g(x.calculateProfitRate(e)),1)])),_:2},1032,["class"])])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("状态")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(1===e.status?"持仓中":2===e.status?"已平仓":"--"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-row-3col"},{default:r((()=>[u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("止盈")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(x.formatPrice(e.takeProfit)),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("止损")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(x.formatPrice(e.stopLoss)),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("时间")])),_:1}),u(D,{class:"hold-value-compact hold-time-compact"},{default:r((()=>[p(g(x.formatTime(e.openTime)),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):y("",!0),"deal"===o.activeTab?(d(),i(w,{key:1,class:"hold-card-list",style:{"min-height":"700rpx"}},{default:r((()=>[o.tabLoading.deal&&0===o.dealList.length?(d(),i(w,{key:0,class:"tab-loading"},{default:r((()=>[u(D,{class:"loading-text"},{default:r((()=>[p("加载中...")])),_:1})])),_:1})):y("",!0),0!==o.dealList.length||o.tabLoading.deal?y("",!0):(d(),i(w,{key:1,class:"empty-state"},{default:r((()=>[u(D,{class:"empty-text"},{default:r((()=>[p("暂无成交数据")])),_:1})])),_:1})),(d(!0),n(f,null,h(o.dealList,(e=>(d(),i(w,{key:e.id,class:"hold-card"},{default:r((()=>[u(w,{class:"hold-card-header"},{default:r((()=>[u(w,{style:{display:"flex","align-items":"center",gap:"8rpx"}},{default:r((()=>[u(D,{class:"hold-symbol"},{default:r((()=>[p(g(x.formatSymbol(e.symbol)),1)])),_:2},1024),u(D,{class:"hold-leverage"},{default:r((()=>[p("x"+g(e.lever||"--"),1)])),_:2},1024)])),_:2},1024),u(D,{class:"hold-direction",style:m(1===e.direction?"background: #02BF87; color: #fff":"background: #F34A69; color: #fff")},{default:r((()=>[p(g(1===e.direction?"买入开多":"卖出开空"),1)])),_:2},1032,["style"])])),_:2},1024),u(w,{class:"hold-row-3col"},{default:r((()=>[u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("数量")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(e.positionAmount||"--"),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("保证金")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(x.formatMargin(e.marginAmount)),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("开仓价")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(e.openPrice||"--"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-row-3col"},{default:r((()=>[u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("盈亏")])),_:1}),u(D,{class:_(["hold-value-compact",e.profit>0?"green":"red"])},{default:r((()=>[p(g(e.profit||"--"),1)])),_:2},1032,["class"])])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("收益率")])),_:1}),u(D,{class:_(["hold-value-compact",e.profit>0?"green":"red"])},{default:r((()=>[p(g(x.formatDealProfitRate(e)),1)])),_:2},1032,["class"])])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("状态")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p("已平仓")])),_:1})])),_:1})])),_:2},1024),u(w,{class:"hold-row-3col"},{default:r((()=>[u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("止盈")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(x.formatPrice(e.takeProfit)),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("止损")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(x.formatPrice(e.stopLoss)),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("时间")])),_:1}),u(D,{class:"hold-value-compact hold-time-compact"},{default:r((()=>[p(g(x.formatTime(e.closeTime)),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128)),o.dealList.length>0?(d(),i(w,{key:2,class:"table-more"},{default:r((()=>[o.dealHasMore?(d(),i(C,{key:0,onClick:x.onDealLoadMore,disabled:o.tabLoading.deal},{default:r((()=>[p("加载更多")])),_:1},8,["onClick","disabled"])):(d(),i(D,{key:1,style:{color:"#fff !important","font-size":"22rpx"}},{default:r((()=>[p("已经没有了")])),_:1}))])),_:1})):y("",!0)])),_:1})):y("",!0),"profit"===o.activeTab?(d(),i(w,{key:2,class:"hold-card-list",style:{"min-height":"700rpx"}},{default:r((()=>[o.tabLoading.profit&&0===o.profitList.length?(d(),i(w,{key:0,class:"tab-loading"},{default:r((()=>[u(D,{class:"loading-text"},{default:r((()=>[p("加载中...")])),_:1})])),_:1})):y("",!0),0!==o.profitList.length||o.tabLoading.profit?y("",!0):(d(),i(w,{key:1,class:"empty-state"},{default:r((()=>[u(D,{class:"empty-text"},{default:r((()=>[p("暂无盈利数据")])),_:1})])),_:1})),(d(!0),n(f,null,h(o.profitList,(e=>(d(),i(w,{key:e.id,class:"hold-card"},{default:r((()=>[u(w,{class:"hold-card-header"},{default:r((()=>[u(w,{style:{display:"flex","align-items":"center",gap:"8rpx"}},{default:r((()=>[u(D,{class:"hold-symbol"},{default:r((()=>[p(g(x.formatSymbol(e.symbol)),1)])),_:2},1024),u(D,{class:"hold-leverage"},{default:r((()=>[p("x"+g(e.lever||"--"),1)])),_:2},1024)])),_:2},1024),u(D,{class:"hold-direction",style:m(1===e.direction?"background: #02BF87; color: #fff":"background: #F34A69; color: #fff")},{default:r((()=>[p(g(1===e.direction?"买入开多":"卖出开空"),1)])),_:2},1032,["style"])])),_:2},1024),u(w,{class:"hold-row-3col"},{default:r((()=>[u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("数量")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(e.positionAmount||"--"),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("保证金")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(x.formatMargin(e.marginAmount)),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("开仓价")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(e.openPrice||"--"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-row-3col"},{default:r((()=>[u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("获利")])),_:1}),u(D,{class:_(["hold-value-compact",e.profit>0?"green":"red"])},{default:r((()=>[p(g(e.profit||"--"),1)])),_:2},1032,["class"])])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("收益率")])),_:1}),u(D,{class:_(["hold-value-compact",e.profit>0?"green":"red"])},{default:r((()=>[p(g(x.formatDealProfitRate(e)),1)])),_:2},1032,["class"])])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("状态")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p("已平仓")])),_:1})])),_:1})])),_:2},1024),u(w,{class:"hold-row-3col"},{default:r((()=>[u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("止盈")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(x.formatPrice(e.takeProfit)),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("止损")])),_:1}),u(D,{class:"hold-value-compact"},{default:r((()=>[p(g(x.formatPrice(e.stopLoss)),1)])),_:2},1024)])),_:2},1024),u(w,{class:"hold-col-compact"},{default:r((()=>[u(D,{class:"hold-label-compact"},{default:r((()=>[p("时间")])),_:1}),u(D,{class:"hold-value-compact hold-time-compact"},{default:r((()=>[p(g(x.formatTime(e.closeTime)),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128)),o.profitList.length>0?(d(),i(w,{key:2,class:"table-more"},{default:r((()=>[o.profitHasMore?(d(),i(C,{key:0,onClick:x.onProfitLoadMore,disabled:o.tabLoading.profit},{default:r((()=>[p("加载更多")])),_:1},8,["onClick","disabled"])):(d(),i(D,{key:1,style:{color:"#fff !important","font-size":"22rpx"}},{default:r((()=>[p("已经没有了")])),_:1}))])),_:1})):y("",!0)])),_:1})):y("",!0)])),_:1}),u(U,{ref:"pairPopup",type:"bottom","mask-click":!0},{default:r((()=>[u(w,{class:"pair-popup"},{default:r((()=>[u(w,{class:"pair-popup-title"},{default:r((()=>[p("选择币对")])),_:1}),u(w,{class:"pair-popup-list"},{default:r((()=>[(d(!0),n(f,null,h(o.trendList,(e=>(d(),i(w,{key:e.pairName,class:_(["pair-popup-item",{active:o.currentPair&&o.currentPair.pairName===e.pairName}]),onClick:t=>x.selectPair(e)},{default:r((()=>[u(E,{src:x.getImageUrl(e.logoUrl),class:"pair-popup-logo",mode:"aspectFit"},null,8,["src"]),u(w,{class:"pair-popup-info"},{default:r((()=>[u(D,{class:"pair-popup-name"},{default:r((()=>[p(g(e.pairName.replace("USDT","")),1)])),_:2},1024),u(D,{class:"pair-popup-token"},{default:r((()=>[p(g(e.tokenName),1)])),_:2},1024)])),_:2},1024),u(w,{class:"pair-popup-price"},{default:r((()=>[u(D,{class:"pair-price"},{default:r((()=>[p(g(x.formatPairPrice(e.price)),1)])),_:2},1024),u(D,{class:_(["pair-change",Number(e.change)>0?"rise":"fall"])},{default:r((()=>[p(g(Number(e.change)>0?"+":"-")+g(Math.abs(Number(e.change)).toFixed(2))+"% ",1)])),_:2},1032,["class"])])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1})])),_:1},512)],64))])),_:1})}],["__scopeId","data-v-83dcd663"]]);export{D as default};
