<template>
	<view class="agreement-detail-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="left-area" @click="handleBack">
					<uni-icons type="left" size="20" color="#fff"></uni-icons>
				</view>
				<text class="page-title">{{ agreementInfo.title }}</text>
				<view class="right-area"></view>
			</view>
		</view>
		<!-- 内容区域 -->
		<scroll-view class="content-box" scroll-y :style="{ height: scrollHeight + 'px' }">
			<view class="agreement-content">
				<rich-text :nodes="formatRichText(agreementInfo.content)" class="content-text"></rich-text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import request from '@/utils/request.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			scrollHeight: 0,
			agreementId: '',
			agreementInfo: {
				title: '',
				content: ''
			},
			from: ''
		}
	},
	onLoad(options) {
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight
		this.scrollHeight = systemInfo.windowHeight
		this.from = options.from || ''
		this.agreementId = options.id
		this.loadAgreementDetail()
	},
	methods: {
		handleBack() {
			if (this.from === 'A') {
				// 如果是从登录/注册页面来的，直接关闭当前页面
				// #ifdef H5
				window.history.back()
				// #endif
				// #ifdef APP-PLUS || MP
				uni.navigateBack()
				// #endif
			} else {
				// 其他来源（如列表页）的返回逻辑保持不变
				uni.navigateBack()
			}
		},
		async loadAgreementDetail() {
			try {
				uni.showLoading({
					title: '加载中...'
				})
				const res = await request({
					url: `/api/agreement/detail/${this.agreementId}`,
					method: 'GET'
				})
				if (res.code === 200 && res.data) {
					this.agreementInfo = {
						title: res.data.title,
						content: typeof res.data.content === 'string' ? res.data.content : JSON.stringify(res.data.content)
					}
				}
			} catch (e) {
				console.error('获取条款详情失败:', e)
				uni.showToast({
					title: '获取条款详情失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		formatRichText(html) {
			if (!html) return ''
			html = html.replace(/color:\s*rgb\(0,\s*0,\s*0\)/gi, 'color: #fff')
			html = html.replace(/color:\s*#000000/gi, 'color: #fff')
			html = html.replace(/color:\s*black/gi, 'color: #fff')
			html = html.replace(/<p>/gi, '<p style="color: #fff;">')
			html = html.replace(/<span>/gi, '<span style="color: #fff;">')
			return html
		}
	}
}
</script>

<style lang="scss" scoped>
.agreement-detail-container {
	min-height: 100vh;
	background: #121212 !important;
	.custom-navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #121212 !important;
		box-shadow: none;
		// border-bottom: 1px solid #222;
		.navbar-content {
			height: 44px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;
			.left-area {
				width: 80rpx;
				height: 44px;
				display: flex;
				align-items: center;
			}
			.page-title {
				color: #fff;
				font-size: 30rpx;
				font-weight: 500;
			}
			.right-area {
				width: 80rpx;
			}
		}
	}
	.content-box {
		padding: calc(var(--status-bar-height) + 44px + 30rpx) 30rpx 40rpx;
		box-sizing: border-box;
		.agreement-content {
			background: #18191D !important;
			border-radius: 16rpx;
			padding: 30rpx;
			// border: 1px solid #FFD70022;
			backdrop-filter: blur(10px);
			.content-text {
				color: #fff;
				font-size: 28rpx;
				line-height: 1.8;
				:deep(img) {
					max-width: 100%;
					height: auto;
				}
				:deep(p) {
					margin: 10rpx 0;
				}
			}
		}
	}
}
</style>